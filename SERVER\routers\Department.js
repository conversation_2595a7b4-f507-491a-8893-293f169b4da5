const { getAllDepartment, getDepartmentById } = require('../controllers/DepartmentController');
const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const departmentRouter = Router();

// 🔒 SECURED ROUTES

// Get all departments - Authenticated users only
departmentRouter.get('/departments', ...authenticatedRoute(), getAllDepartment);

// Get department by ID - Authenticated users only
departmentRouter.get('/departments/:id', ...authenticatedRoute(), getDepartmentById);

module.exports = departmentRouter;