const {
  getAllEmployees,
  addEmployee,
  editEmployee,
  deleteEmployee,
  getEligibleForLoyaltyPay,
  getEligibleForRetirement,
} = require("../controllers/EmployeeListController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const employeeRouter = Router();

// 🔒 SECURED ROUTES

// Get all employees - Authenticated users only
employeeRouter.get("/employees", ...authenticatedRoute(), getAllEmployees);

// Get employees eligible for loyalty pay - Authenticated users only
employeeRouter.get("/employees/loyalty-pay", ...authenticatedRoute(), getEligibleForLoyaltyPay);

// Get employees eligible for retirement - Authenticated users only
employeeRouter.get("/eligible-for-retirement", ...authenticatedRoute(), getEligibleForRetirement);

// Add a new employee - Admin only
employeeRouter.post("/employees", ...adminRoute(), addEmployee);

// Edit an existing employee - Admin only
employeeRouter.put("/employees/:id", ...adminRoute(), editEmployee);

// Delete an employee - Admin only
employeeRouter.delete("/employees/:id", ...adminRoute(), deleteEmployee);

module.exports = employeeRouter;
  
