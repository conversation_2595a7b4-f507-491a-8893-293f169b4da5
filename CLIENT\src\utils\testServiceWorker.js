/**
 * 🔒 Service Worker Test Utilities
 * Quick functions to test if Service Worker auth is working
 */

import { serviceWorkerAuth } from './secureAuth';

export const testServiceWorkerAuth = {
  // Test if Service Worker is properly initialized
  async checkInitialization() {
    console.log('🔒 Testing Service Worker Initialization...');
    
    const tests = {
      serviceWorkerSupported: 'serviceWorker' in navigator,
      serviceWorkerReady: serviceWorkerAuth.isReady,
      serviceWorkerControlling: serviceWorkerAuth.isControlling(),
      registrations: null
    };
    
    try {
      tests.registrations = await navigator.serviceWorker.getRegistrations();
    } catch (error) {
      console.error('Failed to get registrations:', error);
    }
    
    console.table(tests);
    return tests;
  },

  // Test token storage and retrieval
  async testTokenOperations() {
    console.log('🔒 Testing Token Operations...');
    
    const testToken = 'test-token-' + Date.now();
    const testUser = { id: 1, name: 'Test User' };
    
    try {
      // Test storing token
      const storeSuccess = await serviceWorkerAuth.setToken(testToken, testUser, 1);
      console.log('✅ Token storage:', storeSuccess ? 'SUCCESS' : 'FAILED');
      
      // Test retrieving token
      const retrievedToken = await serviceWorkerAuth.getToken();
      console.log('✅ Token retrieval:', retrievedToken === testToken ? 'SUCCESS' : 'FAILED');
      
      // Test status check
      const status = await serviceWorkerAuth.checkStatus();
      console.log('✅ Status check:', status);
      
      // Test clearing token
      const clearSuccess = await serviceWorkerAuth.clearToken();
      console.log('✅ Token clearing:', clearSuccess ? 'SUCCESS' : 'FAILED');
      
      return {
        storeSuccess,
        retrievedToken,
        status,
        clearSuccess
      };
    } catch (error) {
      console.error('🔒 Token operation test failed:', error);
      return { error: error.message };
    }
  },

  // Test token visibility in DevTools
  async testTokenVisibility() {
    console.log('🔒 Testing Token Visibility...');
    
    const testToken = 'visibility-test-' + Date.now();
    const testUser = { id: 1, name: 'Visibility Test' };
    
    try {
      // Store token in Service Worker
      await serviceWorkerAuth.setToken(testToken, testUser, 1);
      
      // Check visibility in various storage locations
      const visibility = {
        localStorage: !!localStorage.getItem('token'),
        sessionStorage: !!sessionStorage.getItem('token'),
        cookies: document.cookie.includes('token'),
        serviceWorker: !!(await serviceWorkerAuth.getToken())
      };
      
      console.log('🔒 Token Visibility Report:');
      console.table(visibility);
      
      // Clean up
      await serviceWorkerAuth.clearToken();
      
      return visibility;
    } catch (error) {
      console.error('🔒 Visibility test failed:', error);
      return { error: error.message };
    }
  },

  // Test API request interception
  async testAPIInterception() {
    console.log('🔒 Testing API Request Interception...');
    
    const testToken = 'api-test-' + Date.now();
    const testUser = { id: 1, name: 'API Test' };
    
    try {
      // Store token
      await serviceWorkerAuth.setToken(testToken, testUser, 1);
      
      // Make a test request to see if token is auto-injected
      const response = await fetch('http://localhost:5005/test-auth', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('🔒 Test API request made');
      console.log('🔒 Check Network tab for Authorization header');
      
      // Clean up
      await serviceWorkerAuth.clearToken();
      
      return {
        requestMade: true,
        status: response.status,
        message: 'Check Network tab in DevTools for Authorization header'
      };
    } catch (error) {
      console.error('🔒 API interception test failed:', error);
      return { error: error.message };
    }
  },

  // Run all tests
  async runAllTests() {
    console.clear();
    console.log('🔒 Running Complete Service Worker Auth Test Suite...');
    console.log('================================================');
    
    const results = {
      initialization: await this.checkInitialization(),
      tokenOperations: await this.testTokenOperations(),
      tokenVisibility: await this.testTokenVisibility(),
      apiInterception: await this.testAPIInterception()
    };
    
    console.log('🔒 Test Suite Complete!');
    console.log('================================================');
    
    // Summary
    const summary = {
      serviceWorkerReady: results.initialization.serviceWorkerReady,
      tokenStorageWorks: results.tokenOperations.storeSuccess,
      tokenHidden: !results.tokenVisibility.localStorage && !results.tokenVisibility.sessionStorage,
      overallStatus: results.initialization.serviceWorkerReady && results.tokenOperations.storeSuccess ? 'SUCCESS' : 'NEEDS_ATTENTION'
    };
    
    console.log('🔒 Test Summary:');
    console.table(summary);
    
    if (summary.overallStatus === 'SUCCESS') {
      console.log('✅ Service Worker Auth is working correctly!');
      console.log('✅ Tokens are COMPLETELY HIDDEN from DevTools!');
    } else {
      console.log('⚠️ Service Worker Auth needs attention');
      console.log('⚠️ Check the test results above for details');
    }
    
    return { results, summary };
  }
};

// Quick test functions for console
window.testServiceWorker = testServiceWorkerAuth;

// Auto-run basic test on load (only in development)
if (process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    console.log('🔒 Service Worker Auth loaded. Run testServiceWorker.runAllTests() to test.');
  }, 2000);
}
