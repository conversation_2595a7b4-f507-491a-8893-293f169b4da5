const express = require('express');
const router = express.Router();

// Import your controller (adjust the path as needed).
const mooeController = require('../controllers/mooeController');
const MOOEProposal = require('../models/mooeProposals');

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Define routes to match the front-end requests - Authenticated users only
router.get('/mooe-data', ...authenticatedRoute(), mooeController.getMOOEData);
router.get('/mooe-entries', ...authenticatedRoute(), mooeController.getMOOEEntries);
router.get('/mooe-status', ...authenticatedRoute(), mooeController.getMOOEStatus);
router.get('/mooe-list', ...authenticatedRoute(), mooeController.getMooeList);
router.get('/MOOEProposal', ...authenticatedRoute(), mooeController.getMOOEData);

// Admin only routes
router.post('/mooe-save', ...adminRoute(), mooeController.bulkSaveMOOE);
router.delete('/mooe-delete', ...adminRoute(), mooeController.deleteAllMooeProposals);

// Get MOOE by parameters - Authenticated users only
router.get("/mooe/getByParams", ...authenticatedRoute(), async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;

    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;

    const mooe = await MOOEProposal.find(query).lean();

    res.status(200).json(mooe);
  } catch (error) {
    console.error("Error fetching MOOE by params:", error);
    res.status(500).json({ message: "Failed to fetch MOOE data" });
  }
});

module.exports = router;
