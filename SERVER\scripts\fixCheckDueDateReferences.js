/**
 * <PERSON><PERSON>t to fix remaining checkDueDate references in router files
 */

const fs = require('fs');
const path = require('path');

const routersDir = path.join(__dirname, '../routers');

// Files that still need to be fixed
const filesToFix = [
  'employeeCourtAppearanceRoutes.js',
  'mealAllowanceRoutes.js', 
  'SpecialCounselAllowanceRoutes.js',
  'subsistenceAllowanceMDSRoutes.js',
  'subsistenceAllowanceSTRoutes.js'
];

const securityImportTemplate = `
// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');`;

function fixRouterFile(fileName) {
  const filePath = path.join(routersDir, fileName);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${fileName}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Replace checkDueDate import
  content = content.replace(
    /const checkDueDate = require\("\.\.\/middleware\/checkDueDate"\);/g,
    securityImportTemplate
  );
  
  // Replace checkDueDate usage in routes
  content = content.replace(
    /(\w+Router\.(get|post|put|delete)\(\s*"[^"]+",\s*)checkDueDate,(\s*\w+)/g,
    '$1...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),$3'
  );
  
  // Add security to GET routes that don't have middleware
  content = content.replace(
    /(\w+Router\.get\(\s*"[^"]+",\s*)(\w+\))/g,
    '$1...authenticatedRoute(), $2'
  );
  
  // Add comment for secured routes
  if (!content.includes('🔒 SECURED ROUTES')) {
    content = content.replace(
      /(const \w+Router = Router\(\);)/,
      '$1\n\n// 🔒 SECURED ROUTES'
    );
  }
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ Fixed: ${fileName}`);
}

function fixAllFiles() {
  console.log('🔧 Fixing remaining checkDueDate references...\n');
  
  filesToFix.forEach(fileName => {
    try {
      fixRouterFile(fileName);
    } catch (error) {
      console.log(`❌ Error fixing ${fileName}:`, error.message);
    }
  });
  
  console.log('\n✅ All files processed!');
}

// Run the fix
if (require.main === module) {
  fixAllFiles();
}

module.exports = { fixRouterFile, fixAllFiles };
