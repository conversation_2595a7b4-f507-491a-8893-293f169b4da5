const express = require("express");
const router = express.Router();
const retireeController = require("../controllers/retireeController");

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get all retirees with pagination - Authenticated users only
router.get("/retiree", ...authenticatedRoute(), retireeController.getAllRetirees);

// Get a single retiree by ID - Authenticated users only
router.get("/retiree/:id", ...authenticatedRoute(), retireeController.getRetireeById);

// Get summary of retirees by type - Authenticated users only
router.get("/retiree/summary", ...authenticatedRoute(), retireeController.getRetireesSummary);

// Create a new retiree record - Admin only with due date check
router.post("/retiree", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), retireeController.createRetiree);

// Update a retiree record - Admin only with due date check
router.put("/retiree/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), retireeController.updateRetiree);

// Delete a retiree record - Admin only with due date check
router.delete("/retiree/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), retireeController.deleteRetiree);

module.exports = router;