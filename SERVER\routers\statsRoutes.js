const express = require("express");
const { getOverviewStats } = require("../controllers/statsController");

const router = express.Router();

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get dashboard overview statistics - Authenticated users only
router.get("/overview", ...authenticatedRoute(), getOverviewStats);

module.exports = router;