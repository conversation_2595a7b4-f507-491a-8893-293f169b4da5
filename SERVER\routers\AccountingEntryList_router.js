const express = require("express");
const router = express.Router();
const { getAccountingEntries, getMooeProposals } = require("../controllers/AccountingEntryList_controller");

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get accounting entries - Authenticated users only
router.get("/accounting-entries", ...authenticatedRoute(), getAccountingEntries);

// Get MOOE proposals - Authenticated users only
router.get("/mooe-proposals", ...authenticatedRoute(), getMooeProposals);

module.exports = router;
