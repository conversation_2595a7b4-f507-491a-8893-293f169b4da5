const {
  getAllSetups,
  getSetupById,
  createSetup,
  updateSetup,
  deleteSetup,
} = require("../controllers/setupController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const setupRouter = Router();

// 🔒 SECURED ROUTES

// Get all setups - Authenticated users only
setupRouter.get("/setups", ...authenticatedRoute(), getAllSetups);

// Get a single setup by ID - Authenticated users only
setupRouter.get("/setups:id", ...authenticatedRoute(), getSetupById);

// Create a new setup - Admin only
setupRouter.post("/setups", ...adminRoute(), createSetup);

// Update a setup by ID - Admin only
setupRouter.put("/setups:id", ...adminRoute(), updateSetup);

// Delete a setup - Admin only
setupRouter.delete("/setups:id", ...adminRoute(), deleteSetup);

module.exports = setupRouter;