const {
  bulkAddCOSPersonnel,
  getAllCOSPersonnel,
  updateCOSPersonnel,
  deleteCOSPersonnel,
  getGrandTotalCOS
} = require("../controllers/COSPersonnelController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const cosPersonnelRouter = Router();

// 🔒 SECURED ROUTES

// Get all COS personnel - Authenticated users only
cosPersonnelRouter.get("/cosPersonnels", ...authenticatedRoute(), getAllCOSPersonnel);

// Get grand total COS - Authenticated users only
cosPersonnelRouter.get("/grandtotalCOS", ...authenticatedRoute(), getGrandTotalCOS);

// Admin routes with due date protection
cosPersonnelRouter.post("/cos-personnel/bulk-add", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), bulkAddCOSPersonnel);
cosPersonnelRouter.put("/cos-personnel/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateCOSPersonnel);
cosPersonnelRouter.delete("/cos-personnel/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteCOSPersonnel);

module.exports = cosPersonnelRouter;
