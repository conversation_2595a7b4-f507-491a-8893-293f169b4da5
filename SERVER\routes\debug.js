const express = require('express');
const router = express.Router();
const { 
  authenticatedRoute, 
  adminRoute,
  checkToken 
} = require('../middleware/securityMiddleware');

// Test route without authentication
router.get('/debug/public', (req, res) => {
  res.json({ 
    message: 'Public route working',
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

// Test route with authentication
router.get('/debug/auth', ...authenticatedRoute(), (req, res) => {
  res.json({ 
    message: 'Authenticated route working',
    user: {
      id: req.user?.id,
      name: req.user?.name,
      email: req.user?.email,
      roles: req.user?.Roles || req.user?.roles
    },
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

// Test route with admin access
router.get('/debug/admin', ...adminRoute(), (req, res) => {
  res.json({ 
    message: 'Admin route working',
    user: {
      id: req.user?.id,
      name: req.user?.name,
      email: req.user?.email,
      roles: req.user?.Roles || req.user?.roles
    },
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

// Test token validation only
router.get('/debug/token', checkToken, (req, res) => {
  res.json({ 
    message: 'Token validation working',
    user: req.user,
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

// Test route to check headers
router.get('/debug/headers', (req, res) => {
  res.json({ 
    message: 'Headers debug',
    headers: {
      authorization: req.headers.authorization,
      'user-agent': req.headers['user-agent'],
      origin: req.headers.origin,
      referer: req.headers.referer
    },
    timestamp: new Date().toISOString(),
    status: 'success'
  });
});

module.exports = router;
