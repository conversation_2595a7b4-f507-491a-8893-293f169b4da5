/**
 * Secure Authentication Middleware
 * Implements HttpOnly cookies for better security
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,        // Cannot be accessed via JavaScript
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'strict',    // CSRF protection
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  path: '/'
};

/**
 * Enhanced login with HttpOnly cookies
 */
const loginWithSecureCookie = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate user credentials (your existing logic)
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password (your existing logic)
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user._id, 
        email: user.email, 
        roles: user.Roles 
      },
      process.env.JWT_SECRET_KEY,
      { expiresIn: '24h' }
    );

    // Set HttpOnly cookie
    res.cookie('authToken', token, COOKIE_OPTIONS);

    // Return user data (without token)
    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        Roles: user.Roles
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
};

/**
 * Secure logout - clears HttpOnly cookie
 */
const logoutWithSecureCookie = (req, res) => {
  res.clearCookie('authToken', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/'
  });

  res.json({ success: true, message: 'Logged out successfully' });
};

/**
 * Enhanced token verification from HttpOnly cookies
 */
const verifySecureCookie = async (req, res, next) => {
  try {
    // Try to get token from HttpOnly cookie first
    let token = req.cookies?.authToken;

    // Fallback to Authorization header for backward compatibility
    if (!token && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    if (!token) {
      return res.status(401).json({ 
        error: 'Authentication required',
        message: 'No authentication token provided'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);

    // Get user from database
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'User not found'
      });
    }

    // Add user to request object
    req.user = {
      id: user._id,
      name: user.name,
      email: user.email,
      Roles: user.Roles
    };

    next();

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      // Clear expired cookie
      res.clearCookie('authToken', COOKIE_OPTIONS);
      return res.status(401).json({ 
        error: 'Token expired',
        message: 'Please log in again'
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'Authentication failed'
      });
    }

    console.error('Auth verification error:', error);
    res.status(500).json({ 
      error: 'Authentication error',
      message: 'Internal server error'
    });
  }
};

/**
 * Verify authentication endpoint
 */
const verifyAuthStatus = (req, res) => {
  // This endpoint uses verifySecureCookie middleware
  res.json({
    authenticated: true,
    user: req.user
  });
};

/**
 * Enhanced security headers middleware
 */
const securityHeaders = (req, res, next) => {
  // Prevent XSS attacks
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Prevent information disclosure
  res.removeHeader('X-Powered-By');
  
  // Content Security Policy
  res.setHeader('Content-Security-Policy', 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: https:; " +
    "connect-src 'self';"
  );

  next();
};

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = {
  attempts: new Map(),
  
  middleware: (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    return (req, res, next) => {
      const ip = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      
      if (!authRateLimit.attempts.has(ip)) {
        authRateLimit.attempts.set(ip, { count: 1, resetTime: now + windowMs });
        return next();
      }
      
      const attempts = authRateLimit.attempts.get(ip);
      
      if (now > attempts.resetTime) {
        // Reset window
        attempts.count = 1;
        attempts.resetTime = now + windowMs;
        return next();
      }
      
      if (attempts.count >= maxAttempts) {
        return res.status(429).json({
          error: 'Too many attempts',
          message: 'Please try again later',
          retryAfter: Math.ceil((attempts.resetTime - now) / 1000)
        });
      }
      
      attempts.count++;
      next();
    };
  }
};

/**
 * Session fingerprinting for additional security
 */
const sessionFingerprint = (req, res, next) => {
  const fingerprint = {
    userAgent: req.headers['user-agent'],
    acceptLanguage: req.headers['accept-language'],
    acceptEncoding: req.headers['accept-encoding']
  };
  
  req.fingerprint = Buffer.from(JSON.stringify(fingerprint)).toString('base64');
  next();
};

module.exports = {
  loginWithSecureCookie,
  logoutWithSecureCookie,
  verifySecureCookie,
  verifyAuthStatus,
  securityHeaders,
  authRateLimit,
  sessionFingerprint,
  COOKIE_OPTIONS
};
