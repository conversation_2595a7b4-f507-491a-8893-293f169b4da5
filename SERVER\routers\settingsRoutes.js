const {
  getAllSettings,
  createSettings,
  updateSettings,
  deleteSettings,
  getActiveSettings,
  setActiveFiscalYear,
  getAdvancedSettings,    // bagong function para kuhanin ang advanced settings
  updateAdvancedSettings, // bagong function para i-update ang advanced settings
} = require("../controllers/settingsController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const settingsRouter = Router();

// 🔒 SECURED ROUTES

// Get all settings - Authenticated users only
settingsRouter.get("/settings", ...authenticatedRoute(), getAllSettings);

// Get active fiscal year settings - Authenticated users only
settingsRouter.get("/settings/active", ...authenticatedRoute(), getActiveSettings);

// Kunin ang advanced settings para sa isang partikular na fiscal year - Authenticated users only
settingsRouter.get("/settings/advanced/:fiscalYear", ...authenticatedRoute(), getAdvancedSettings);

// Create new settings - Admin only
settingsRouter.post("/settings", ...adminRoute(), createSettings);

// Update an existing settings entry - Admin only
settingsRouter.put("/settings/:id", ...adminRoute(), updateSettings);

// Delete a settings entry - Admin only
settingsRouter.delete("/settings/:id", ...adminRoute(), deleteSettings);

// Set a specific fiscal year as active - Admin only
settingsRouter.patch("/settings/:id/activate", ...adminRoute(), setActiveFiscalYear);

// I-update ang advanced settings para sa isang partikular na fiscal year - Admin only
settingsRouter.put("/settings/advanced/:fiscalYear", ...adminRoute(), updateAdvancedSettings);

module.exports = settingsRouter;
