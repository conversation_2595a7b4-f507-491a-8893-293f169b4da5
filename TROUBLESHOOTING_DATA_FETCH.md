# 🚨 Troubleshooting: No Data Fetch After Security Implementation

## Quick Diagnosis Steps

### 1. **Add Debug Component to Any Page**

Add this to any page that's not loading data:

```jsx
// Add to the top of your component file
import DebugAuth from '../components/DebugAuth';

// Add inside your component's return statement
return (
  <div>
    {/* Your existing content */}
    <DebugAuth />
  </div>
);
```

### 2. **Check Browser Console**

Open browser console (F12) and look for:
- ❌ 401 Unauthorized errors
- ❌ 403 Forbidden errors  
- ❌ Network errors
- ❌ Token decryption errors

### 3. **Test Debug Endpoints**

Visit these URLs in your browser:
- `http://localhost:3001/debug/public` (should work)
- `http://localhost:3001/debug/headers` (check if token is sent)
- `http://localhost:3001/debug/auth` (test authentication)

## Common Issues & Solutions

### Issue 1: "401 Unauthorized" Errors

**Symptoms:**
- All API calls return 401
- User appears logged in but data won't load

**Causes & Solutions:**

#### A. Token Missing or Invalid
```javascript
// Check in browser console:
console.log('Token:', localStorage.getItem('token'));

// If null or undefined:
// 1. Log out and log back in
// 2. Check if AUTH_SERVER_URL is correct
// 3. Verify SECRET_KEY matches between client and server
```

#### B. Token Decryption Issues
```javascript
// Test token decryption:
import { debugAuth } from './src/utils/debugAuth';
debugAuth(); // Run in console
```

**Fix:** Ensure `SECRET_KEY` in client `.env` matches server

#### C. JWT Token Expired
```javascript
// Check token expiry in console after running debugAuth()
// If expired, user needs to log in again
```

### Issue 2: "403 Forbidden" Errors

**Symptoms:**
- Some pages load, others don't
- User has insufficient permissions

**Solutions:**

#### A. Check User Roles
```javascript
// In browser console:
console.log('User roles:', currentUser?.Roles);

// Should show: ["SUPER ADMIN"] or ["BUDGET MANAGER"] etc.
// If empty or wrong roles, update in database
```

#### B. Verify Route Permissions
Check `SERVER/config/rolePermissions.js` for correct permissions

### Issue 3: Network/CORS Issues

**Symptoms:**
- Network errors in console
- CORS policy errors

**Solutions:**

#### A. Check Server URL
```javascript
// In client .env file:
VITE_SERVER_URL=http://localhost:3001
```

#### B. Verify Server is Running
```bash
# Check if server is running on correct port
curl http://localhost:3001/debug/public
```

### Issue 4: Environment Variables

**Check these files:**

#### Client `.env`:
```
VITE_SERVER_URL=http://localhost:3001
VITE_AUTH_SERVER_URL=http://localhost:3000
VITE_SECRET_KEY=your-secret-key
VITE_AUTH_CLIENT_URL=http://localhost:3000
```

#### Server `.env`:
```
JWT_SECRET_KEY=your-secret-key
APP_PORT=3001
CLIENT1=http://localhost:5173
CLIENT2=http://localhost:3000
```

## Step-by-Step Debugging

### Step 1: Verify Authentication Flow

1. **Check if user is logged in:**
   ```javascript
   // In any component
   const { currentUser } = useUser();
   console.log('Current user:', currentUser);
   ```

2. **Check if token exists:**
   ```javascript
   console.log('Token exists:', !!localStorage.getItem('token'));
   ```

3. **Test token decryption:**
   ```javascript
   import { debugAuth } from './src/utils/debugAuth';
   debugAuth();
   ```

### Step 2: Test API Endpoints

1. **Test public endpoint:**
   ```bash
   curl http://localhost:3001/debug/public
   ```

2. **Test with token:**
   ```bash
   # Get token from localStorage and test
   curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3001/debug/auth
   ```

### Step 3: Check Server Logs

Look for these in server console:
- ✅ "🚀: http://localhost:3001" (server started)
- ❌ "Auth Middleware Error:" (authentication issues)
- ❌ "Authorization error:" (permission issues)

### Step 4: Database Check

Verify user roles in database:
```javascript
// In MongoDB or your database
db.users.findOne({ email: "<EMAIL>" })
// Should have Roles field with array like ["SUPER ADMIN"]
```

## Quick Fixes

### Fix 1: Reset Authentication
```javascript
// Clear everything and re-login
localStorage.clear();
window.location.href = 'http://localhost:3000/login';
```

### Fix 2: Temporary Bypass (Development Only)
```javascript
// In SERVER/middleware/check_token.js, temporarily add:
if (process.env.NODE_ENV === 'development') {
  req.user = {
    id: 'dev-user',
    name: 'Dev User',
    email: '<EMAIL>',
    Roles: ['SUPER ADMIN']
  };
  return next();
}
```

### Fix 3: Update User Roles in Database
```javascript
// If user has wrong roles, update in database:
// MongoDB example:
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { Roles: ["SUPER ADMIN"] } }
);
```

## Testing Checklist

- [ ] Server is running on correct port
- [ ] Client can reach server (test public endpoint)
- [ ] User is logged in (check currentUser)
- [ ] Token exists in localStorage
- [ ] Token can be decrypted
- [ ] Token is not expired
- [ ] User has correct roles
- [ ] API requests include Authorization header
- [ ] Environment variables are correct

## Emergency Rollback

If you need to temporarily disable security:

1. **Comment out security middleware in routes:**
   ```javascript
   // router.get("/data", ...authenticatedRoute(), controller.getData);
   router.get("/data", controller.getData); // Temporarily unsecured
   ```

2. **Or create bypass in check_token.js:**
   ```javascript
   // Add at top of middleware
   if (process.env.BYPASS_AUTH === 'true') {
     req.user = { id: 'bypass', Roles: ['SUPER ADMIN'] };
     return next();
   }
   ```

## Get Help

If issues persist:

1. **Run full debug:**
   ```javascript
   import { fullAuthDebug } from './src/utils/debugAuth';
   fullAuthDebug(currentUser);
   ```

2. **Check all debug endpoints:**
   - `/debug/public`
   - `/debug/headers` 
   - `/debug/token`
   - `/debug/auth`
   - `/debug/admin`

3. **Share console output and error messages**
