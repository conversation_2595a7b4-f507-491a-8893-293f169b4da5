const {
    getAllPS_Annexes,
    addPS_Annex,
    editPS_Annex,
    deletePS_Annex,
    getPersonnelServices,
    getPersonnelServicesLegal,
  } = require("../controllers/ps_annexes_controller");
  
  const Router = require("express").Router;
  const psAnnexesRouter = Router();
  
  // Get all PS_Annexes
  psAnnexesRouter.get("/ps_annexes", getAllPS_Annexes);
  
  // Add a new PS_Annex
  psAnnexesRouter.post("/ps_annexes", addPS_Annex);
  
  // Edit an existing PS_Annex
  psAnnexesRouter.put("/ps_annexes/:id", editPS_Annex);
  
  // Delete a PS_Annex
  psAnnexesRouter.delete("/ps_annexes/:id", deletePS_Annex);

  psAnnexesRouter.get("/ps_employeename", getPersonnelServices);

  psAnnexesRouter.get("/ps_legal", getPersonnelServicesLegal);
  
  module.exports = psAnnexesRouter;