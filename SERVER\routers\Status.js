const { getStatus, getStatusById } = require('../controllers/StatusController');
const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const statusRouter = Router();

// 🔒 SECURED ROUTES

// Get all status - Authenticated users only
statusRouter.get('/status', ...authenticatedRoute(), getStatus);

// Get status by ID - Authenticated users only
statusRouter.get('/byidstatus/:id', ...authenticatedRoute(), getStatusById);

module.exports = statusRouter;