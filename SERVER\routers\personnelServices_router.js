// Import controller functions individually to avoid issues
const { 
  bulkAddPersonnelServices,
  updatePersonnelService,
  getAllPersonnelServices,
  getAllPerServices,
  getGrandTotalPermanent,
  getGrandTotalCasual,
  deleteAllPersonnelss,
  getGrandTotal,
  getPersonnelHiredBeforeJune1988
} = require("../controllers/personnel_service_controller");

// Import the model directly to ensure it's available
const PersonnelServicesModel = require("../models/PersonnelServices");

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const Router = require("express").Router;

const personnelServicesRouter = Router();

// 🔒 SECURED ROUTES

// Admin routes with due date protection
personnelServicesRouter.post(
  "/personnelServices/bulk-add",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  bulkAddPersonnelServices ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

personnelServicesRouter.put(
  "/personnelServices/:id",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  updatePersonnelService ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

// Authenticated GET routes
personnelServicesRouter.get(
  "/personnelServices",
  ...authenticatedRoute(),
  getAllPersonnelServices ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

personnelServicesRouter.get(
  "/getpersonnels",
  ...authenticatedRoute(),
  getAllPerServices ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

personnelServicesRouter.get(
  "/grandtotalPermanent",
  ...authenticatedRoute(),
  getGrandTotalPermanent ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

personnelServicesRouter.get(
  "/grandtotalCasual",
  ...authenticatedRoute(),
  getGrandTotalCasual ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

// Admin only routes
personnelServicesRouter.post(
  "/deleteAllPersonnels",
  ...adminRoute(),
  deleteAllPersonnelss ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

personnelServicesRouter.get(
  "/grandtotalAll",
  ...authenticatedRoute(),
  getGrandTotal ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

// Get personnel by parameters - Authenticated users only
personnelServicesRouter.get("/getpersonnels/byParams", ...authenticatedRoute(), async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;

    console.log("Fetching personnel with params:", { fiscalYear, budgetType, processBy, region, status });

    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;

    // Ensure we have a model to work with
    if (!PersonnelServicesModel) {
      console.error("PersonnelServices model is not available");
      return res.status(500).json({ message: "Internal server error: Model not available" });
    }

    const personnel = await PersonnelServicesModel.find(query).lean();

    console.log(`Found ${personnel.length} personnel records`);
    res.status(200).json(personnel);
  } catch (error) {
    console.error("Error fetching personnel by params:", error);
    res.status(500).json({ message: "Failed to fetch personnel data" });
  }
});

// Keep the original route for backward compatibility - Authenticated users only
personnelServicesRouter.get("/api/personnel/getByParams", ...authenticatedRoute(), async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;

    console.log("Fetching personnel with params (legacy endpoint):", { fiscalYear, budgetType, processBy, region, status });

    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;

    // Ensure we have a model to work with
    if (!PersonnelServicesModel) {
      console.error("PersonnelServices model is not available");
      return res.status(500).json({ message: "Internal server error: Model not available" });
    }

    const personnel = await PersonnelServicesModel.find(query).lean();

    console.log(`Found ${personnel.length} personnel records`);
    res.status(200).json(personnel);
  } catch (error) {
    console.error("Error fetching personnel by params:", error);
    res.status(500).json({ message: "Failed to fetch personnel data" });
  }
});


// personnelServicesRouter.get(
//   "/getpersonnels/hiredBeforeJune1988", 
//   getPersonnelHiredBeforeJune1988 || 
//     ((req, res) => res.status(500).json({ message: "Function not available" }))
// );

// Add the new route for syncing meal allowances - Admin only
personnelServicesRouter.post(
  "/personnelServices/sync-meal-allowances",
  ...adminRoute(),
  exports.syncAllMealAllowances ||
    ((req, res) => res.status(500).json({ message: "Function not available" }))
);

module.exports = personnelServicesRouter;
