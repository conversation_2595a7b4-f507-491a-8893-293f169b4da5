// routes/fiscalYearRoutes.js

const express = require('express');
const fiscalYearrouter = express.Router();
const fiscalYearController = require('../controllers/fiscalYearController');

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Route to fetch all fiscal years - Authenticated users only
fiscalYearrouter.get('/getfy', ...authenticatedRoute(), fiscalYearController.getFiscalYears);

// Route to create a new fiscal year - Admin only
fiscalYearrouter.post('/create', ...adminRoute(), fiscalYearController.createFiscalYear);

module.exports = fiscalYearrouter;
