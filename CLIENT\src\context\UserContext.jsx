import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { authApi } from "../config/api";
import Loading from "../global/components/Loading";
import env from "../utils/env";
import CryptoJS from "crypto-js"; // Import CryptoJS for encryption
import { serviceWorkerAuth, secureAuthWithEncryption } from "../utils/secureAuth"; // Import Service Worker auth
import securityConfig from "../config/securityConfig"; // Import security configuration

const UserContext = createContext({
  currentUser: null,
  setCurrentUser: async () => {},
  isLoading: false,
  error: null,
  logout: async () => {},
});

const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const nav = useNavigate();

  // 🔒 Enhanced token storage with configurable security modes
  const storeToken = async (token) => {
    try {
      const mode = securityConfig.mode;
      console.log(`🔒 Storing token using ${mode} security mode`);

      // Store based on security configuration
      if (securityConfig.shouldUseLocalStorageFallback()) {
        // Store encrypted version for cross-browser access
        if (securityConfig.shouldEncrypt()) {
          const encryptedToken = CryptoJS.AES.encrypt(token, env("SECRET_KEY")).toString();
          localStorage.setItem("token", encryptedToken);
          console.log('🔒 Token stored in encrypted localStorage (cross-browser support)');
        } else {
          localStorage.setItem("token", token);
          console.log('🔒 Token stored in plain localStorage');
        }
      }

      // 🚨 TEMPORARILY DISABLED - Service Worker causing issues
      // if (securityConfig.shouldUseServiceWorker() && serviceWorkerAuth.isReady) {
      //   const success = await serviceWorkerAuth.setToken(token, currentUser, 60);
      //   if (success) {
      //     console.log('🔒 Token ALSO stored in Service Worker - HIDDEN from DevTools!');
      //
      //     // For MAXIMUM mode, clear localStorage for complete hiding
      //     if (mode === 'MAXIMUM') {
      //       localStorage.removeItem("token");
      //       console.log('🔒 localStorage cleared - using Service Worker only (MAXIMUM security)');
      //     }
      //   }
      // }
    } catch (error) {
      console.error('🔒 Token storage error:', error);
      // Final fallback to plain localStorage
      localStorage.setItem("token", token);
    }
  };

  // 🔒 Enhanced token retrieval with Service Worker + Fallback support
  const getToken = async () => {
    try {
      // 🚨 TEMPORARILY DISABLED - Service Worker causing issues
      // if (serviceWorkerAuth.isReady) {
      //   const token = await serviceWorkerAuth.getToken();
      //   if (token) {
      //     console.log('🔒 Token retrieved from Service Worker (HIDDEN)');
      //     return token;
      //   }
      // }

      // Fallback to encrypted localStorage (cross-browser support)
      const encryptedToken = localStorage.getItem("token");
      if (!encryptedToken) {
        console.log('🔒 No token found in any storage');
        return null;
      }

      try {
        const bytes = CryptoJS.AES.decrypt(encryptedToken, env("SECRET_KEY"));
        const decryptedToken = bytes.toString(CryptoJS.enc.Utf8);
        console.log('🔒 Token retrieved from encrypted localStorage (cross-browser)');
        return decryptedToken;
      } catch (error) {
        console.error("🔒 Error decrypting token:", error);
        console.log('🔒 Token retrieved from localStorage (fallback)');
        return encryptedToken; // Return as-is if decryption fails
      }
    } catch (error) {
      console.error('🔒 Token retrieval error:', error);
      return localStorage.getItem("token"); // Final fallback
    }
  };

  // 🔒 Enhanced user initialization with Service Worker
  const handleSetUserInitialization = async () => {
    try {
      const res = await authApi.get("/get-user");
      setCurrentUser(res.data.user);
      if (res.data.jwtToken) {
        await storeToken(res.data.jwtToken);
        console.log('🔒 User initialized with secure token storage');
      }
    } catch (error) {
      console.error('🔒 User initialization error:', error);
    }
  };

  // 🔒 Enhanced logout with Service Worker support
  const handleLogout = async () => {
    try {
      await authApi.get("/logout");

      // Clear from Service Worker (HIDDEN)
      if (serviceWorkerAuth.isReady) {
        await serviceWorkerAuth.clearToken();
        console.log('🔒 Token cleared from Service Worker');
      }

      // Clear fallback storage
      localStorage.removeItem("token");
      sessionStorage.removeItem("token");

      setCurrentUser(null);
      console.log('🔒 Logout successful - All tokens cleared');
      location.href = env("AUTH_CLIENT_URL") + "/login";
    } catch (error) {
      console.error('🔒 Logout error:', error);
      // Force clear even if API call fails
      localStorage.removeItem("token");
      sessionStorage.removeItem("token");
      setCurrentUser(null);
      location.href = env("AUTH_CLIENT_URL") + "/login";
    }
  };

  useEffect(() => {
    const fetchUser = async () => {
      try {
        // Check if user is already authenticated via Service Worker
        if (serviceWorkerAuth.isReady) {
          const status = await serviceWorkerAuth.checkStatus();
          if (status.isValid && status.user) {
            console.log('🔒 User authenticated via Service Worker');
            setCurrentUser(status.user);
            setIsLoading(false);
            return;
          }
        }

        // Fetch user from API
        const res = await authApi.get("/get-user");
        if (res.data.user) {
          setCurrentUser(res.data.user);
          if (res.data.jwtToken) {
            await storeToken(res.data.jwtToken);
            console.log('🔒 User fetched and token stored securely');
          }
        } else {
          // Clear tokens and redirect to SSO login
          localStorage.removeItem("token");
          console.log('🔒 No user found, redirecting to SSO login');
          location.href = env("AUTH_CLIENT_URL") + "/login";
        }
      } catch (error) {
        console.error("🔒 Error fetching user:", error);

        // Clear tokens and redirect to SSO login on error
        localStorage.removeItem("token");
        console.log('🔒 Auth error, redirecting to SSO login');
        location.href = env("AUTH_CLIENT_URL") + "/login";
      } finally {
        setIsLoading(false);
      }
    };

    // Wait a bit for Service Worker to initialize, but don't wait too long
    setTimeout(fetchUser, 100);
  }, [nav]);

  // 🔒 Enhanced context value with Service Worker support
  const value = {
    currentUser,
    setCurrentUser: handleSetUserInitialization,
    logout: handleLogout,
    isLoading,
    jwtToken: getToken(), // This is now async, but kept for compatibility

    // Service Worker specific methods
    getSecureToken: getToken,
    storeSecureToken: storeToken,
    serviceWorkerReady: serviceWorkerAuth.isReady,

    // Security utilities
    getSecurityReport: () => ({
      serviceWorkerReady: serviceWorkerAuth.isReady,
      serviceWorkerControlling: serviceWorkerAuth.isControlling(),
      tokenVisibleInDevTools: !!localStorage.getItem('token'),
      securityLevel: serviceWorkerAuth.isReady ? 'MAXIMUM (Service Worker)' : 'BASIC (localStorage)',
      recommendation: serviceWorkerAuth.isReady
        ? '✅ Token is COMPLETELY HIDDEN from DevTools'
        : '⚠️ Token may be visible in DevTools'
    })
  };

  if (isLoading) return <Loading />;

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export default UserProvider;
export const useUser = () => useContext(UserContext);