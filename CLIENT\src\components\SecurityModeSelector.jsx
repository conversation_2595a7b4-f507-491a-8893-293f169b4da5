import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Alert,
  Chip,
  Stack,
  Button,
  Collapse
} from '@mui/material';
import { Security, Visibility, VisibilityOff, Language, Shield } from '@mui/icons-material';
import securityConfig, { SECURITY_MODES } from '../config/securityConfig';

const SecurityModeSelector = () => {
  const [selectedMode, setSelectedMode] = useState(securityConfig.mode);
  const [showDetails, setShowDetails] = useState(false);

  const handleModeChange = (event) => {
    setSelectedMode(event.target.value);
  };

  const applyMode = () => {
    // Note: This would require app restart to fully apply
    console.log(`🔒 Security mode changed to: ${selectedMode}`);
    alert(`Security mode will be changed to ${selectedMode}. Please refresh the page to apply changes.`);
  };

  const getModeIcon = (mode) => {
    switch (mode) {
      case SECURITY_MODES.MAXIMUM:
        return <Shield color="success" />;
      case SECURITY_MODES.BALANCED:
        return <Security color="primary" />;
      case SECURITY_MODES.CROSS_BROWSER:
        return <Language color="info" />;
      case SECURITY_MODES.BASIC:
        return <Visibility color="warning" />;
      default:
        return <Security />;
    }
  };

  const getModeColor = (mode) => {
    switch (mode) {
      case SECURITY_MODES.MAXIMUM:
        return 'success';
      case SECURITY_MODES.BALANCED:
        return 'primary';
      case SECURITY_MODES.CROSS_BROWSER:
        return 'info';
      case SECURITY_MODES.BASIC:
        return 'warning';
      default:
        return 'default';
    }
  };

  const currentModeInfo = securityConfig.getCurrentMode();
  const securityReport = securityConfig.getSecurityReport();

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Security sx={{ mr: 1 }} />
        <Typography variant="h6" fontWeight="bold">
          🔒 Security Mode Configuration
        </Typography>
      </Box>

      {/* Current Mode Display */}
      <Alert 
        severity={getModeColor(securityConfig.mode)} 
        sx={{ mb: 3 }}
        icon={getModeIcon(securityConfig.mode)}
      >
        <Typography variant="subtitle2" fontWeight="bold">
          Current Mode: {currentModeInfo.name}
        </Typography>
        <Typography variant="body2">
          {currentModeInfo.description}
        </Typography>
      </Alert>

      {/* Mode Selector */}
      <FormControl component="fieldset" fullWidth sx={{ mb: 3 }}>
        <FormLabel component="legend" sx={{ fontWeight: 'bold', mb: 2 }}>
          Choose Security Level:
        </FormLabel>
        <RadioGroup value={selectedMode} onChange={handleModeChange}>
          {Object.entries(SECURITY_MODES).map(([key, mode]) => {
            const modeInfo = securityConfig.descriptions[mode];
            return (
              <Paper 
                key={mode} 
                variant="outlined" 
                sx={{ 
                  p: 2, 
                  mb: 1,
                  border: selectedMode === mode ? 2 : 1,
                  borderColor: selectedMode === mode ? `${getModeColor(mode)}.main` : 'divider'
                }}
              >
                <FormControlLabel
                  value={mode}
                  control={<Radio color={getModeColor(mode)} />}
                  label={
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        {getModeIcon(mode)}
                        <Typography variant="subtitle1" fontWeight="bold" sx={{ ml: 1 }}>
                          {modeInfo.name}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {modeInfo.description}
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap">
                        <Chip 
                          size="small" 
                          label={modeInfo.crossBrowser ? "Cross-Browser" : "Single Browser"} 
                          color={modeInfo.crossBrowser ? "success" : "warning"}
                          variant="outlined"
                        />
                        <Chip 
                          size="small" 
                          label={`DevTools: ${modeInfo.devToolsVisible === true ? "Visible" : modeInfo.devToolsVisible === false ? "Hidden" : modeInfo.devToolsVisible}`}
                          color={modeInfo.devToolsVisible === false ? "success" : "warning"}
                          variant="outlined"
                        />
                        <Chip 
                          size="small" 
                          label={`XSS Protection: ${modeInfo.xssProtection}`}
                          color={modeInfo.xssProtection === "Excellent" ? "success" : modeInfo.xssProtection === "Very Good" ? "primary" : "warning"}
                          variant="outlined"
                        />
                      </Stack>
                    </Box>
                  }
                />
              </Paper>
            );
          })}
        </RadioGroup>
      </FormControl>

      {/* Apply Button */}
      {selectedMode !== securityConfig.mode && (
        <Box sx={{ mb: 3 }}>
          <Button 
            variant="contained" 
            color={getModeColor(selectedMode)}
            onClick={applyMode}
            startIcon={<Security />}
          >
            Apply {securityConfig.descriptions[selectedMode].name} Mode
          </Button>
        </Box>
      )}

      {/* Security Report */}
      <Button 
        variant="outlined" 
        onClick={() => setShowDetails(!showDetails)}
        sx={{ mb: 2 }}
      >
        {showDetails ? 'Hide' : 'Show'} Security Report
      </Button>

      <Collapse in={showDetails}>
        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default' }}>
          <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
            Current Security Status:
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body2">
              <strong>Configured Mode:</strong> {securityReport.configuredMode}
            </Typography>
            <Typography variant="body2">
              <strong>Effective Mode:</strong> {securityReport.effectiveMode}
            </Typography>
            <Typography variant="body2">
              <strong>Service Worker Supported:</strong> {securityReport.serviceWorkerSupported ? '✅ Yes' : '❌ No'}
            </Typography>
            <Typography variant="body2">
              <strong>Service Worker Ready:</strong> {securityReport.serviceWorkerReady ? '✅ Yes' : '❌ No'}
            </Typography>
          </Stack>
          
          {securityReport.recommendations.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                Recommendations:
              </Typography>
              {securityReport.recommendations.map((rec, index) => (
                <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                  {rec}
                </Typography>
              ))}
            </Box>
          )}
        </Paper>
      </Collapse>

      {/* Quick Instructions */}
      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="body2">
          <strong>Quick Guide:</strong><br/>
          • <strong>MAXIMUM:</strong> Most secure, single browser only<br/>
          • <strong>BALANCED:</strong> Secure + cross-browser (recommended)<br/>
          • <strong>CROSS_BROWSER:</strong> Works everywhere, encrypted<br/>
          • <strong>BASIC:</strong> Maximum compatibility, less secure
        </Typography>
      </Alert>
    </Paper>
  );
};

export default SecurityModeSelector;
