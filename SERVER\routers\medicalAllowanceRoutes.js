const Router = require("express").Router;
const {
  getAllMedicalAllowances,
  getMedicalAllowance,
  createMedicalAllowance,
  updateMedicalAllowance,
  deleteMedicalAllowance,
  bulkAddMedicalAllowances,
  syncAllMedicalAllowances,
} = require("../controllers/medicalAllowanceController");
// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const medicalAllowanceRouter = Router();

// 🔒 SECURED ROUTES

// List all medical allowance records - Authenticated users only
medicalAllowanceRouter.get("/medical-allowance", ...authenticatedRoute(), getAllMedicalAllowances);

// Retrieve a single medical allowance record - Authenticated users only
medicalAllowanceRouter.get("/medical-allowance/:id", ...authenticatedRoute(), getMedicalAllowance);

// Create a new medical allowance record - Admin only with due date check
medicalAllowanceRouter.post(
  "/medical-allowance",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  createMedicalAllowance
);

// Update an existing medical allowance record - Admin only with due date check
medicalAllowanceRouter.put(
  "/medical-allowance/:id",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  updateMedicalAllowance
);

// Delete a medical allowance record - Admin only with due date check
medicalAllowanceRouter.delete(
  "/medical-allowance/:id",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  deleteMedicalAllowance
);

// Bulk add medical allowances for eligible employees - Admin only with due date check
medicalAllowanceRouter.post(
  "/medical-allowance/bulk-add",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  bulkAddMedicalAllowances
);

// Synchronize all medical allowances with personnel services - Admin only
medicalAllowanceRouter.post(
  "/medical-allowance/sync-all",
  ...adminRoute(),
  syncAllMedicalAllowances
);

module.exports = medicalAllowanceRouter;