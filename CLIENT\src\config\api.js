import axios from "axios";
import env from "../utils/env";
import CryptoJS from "crypto-js"; // Import CryptoJS

// Function to get the stored token (decrypt it first)
const getToken = () => {
  const encryptedToken = localStorage.getItem("token");
  if (!encryptedToken) return null;
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedToken, env("SECRET_KEY"));
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error("Error decrypting token:", error);
    return null;
  }
};

const api = axios.create({
  baseURL: env("SERVER_URL"),
  withCredentials: true,
});

// Enhanced token attachment with Service Worker support
api.interceptors.request.use(
  async (config) => {
    let token = null;

    // Try Service Worker first
    try {
      if (navigator.serviceWorker && navigator.serviceWorker.controller) {
        const { serviceWorkerAuth } = await import('../utils/secureAuth');
        if (serviceWorkerAuth.isReady) {
          token = await serviceWorkerAuth.getToken();
          if (token) {
            console.log('🔒 API: Token from Service Worker');
          }
        }
      }
    } catch (error) {
      console.warn('🔒 Service Worker token retrieval failed:', error);
    }

    // Fallback to localStorage
    if (!token) {
      token = getToken();
      if (token) {
        console.log('🔒 API: Token from localStorage (fallback)');
      }
    }

    // Attach token if available
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.warn('🔒 API: No token available');
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Enhanced error handling with Service Worker support
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token is invalid or expired
      console.warn('🔒 Authentication failed, clearing tokens...');

      // Clear from localStorage (fallback)
      localStorage.removeItem("token");

      // Clear from Service Worker if available
      if (navigator.serviceWorker && navigator.serviceWorker.controller) {
        try {
          const { serviceWorkerAuth } = await import('../utils/secureAuth');
          await serviceWorkerAuth.clearToken();
          console.log('🔒 Token cleared from Service Worker');
        } catch (e) {
          console.warn('🔒 Could not clear Service Worker token:', e);
        }
      }

      // Redirect to login
      window.location.href = env("AUTH_CLIENT_URL") + "/login";
    } else if (error.response?.status === 403) {
      // Insufficient permissions
      console.warn('🔒 Access denied:', error.response.data.message);
      // You can show a toast notification here if you have one
    }
    return Promise.reject(error);
  }
);

export default api;

export const authApi = axios.create({
  baseURL: env("AUTH_SERVER_URL"),
  withCredentials: true,
});