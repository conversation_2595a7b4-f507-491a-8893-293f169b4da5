import axios from "axios";
import env from "../utils/env";
import CryptoJS from "crypto-js"; // Import CryptoJS

// Function to get the stored token (decrypt it first)
const getToken = () => {
  const encryptedToken = localStorage.getItem("token");
  if (!encryptedToken) return null;
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedToken, env("SECRET_KEY"));
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error("Error decrypting token:", error);
    return null;
  }
};

const api = axios.create({
  baseURL: env("SERVER_URL"),
  withCredentials: true,
});

// Attach token to requests
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle authentication errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token is invalid or expired
      console.warn('Authentication failed, redirecting to login...');
      localStorage.removeItem("token");
      window.location.href = env("AUTH_CLIENT_URL") + "/login";
    } else if (error.response?.status === 403) {
      // Insufficient permissions
      console.warn('Access denied:', error.response.data.message);
      // You can show a toast notification here if you have one
    }
    return Promise.reject(error);
  }
);

export default api;

export const authApi = axios.create({
  baseURL: env("AUTH_SERVER_URL"),
  withCredentials: true,
});