/**
 * Secure Authentication Utilities
 * Uses HttpOnly cookies instead of localStorage for better security
 */

import api from '../config/api';

// Option 1: HttpOnly Cookies (Most Secure)
export const secureAuthWithCookies = {
  // Login function that stores token in HttpOnly cookie
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);
      
      // Server should set HttpOnly cookie automatically
      // No need to store token in localStorage
      
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Logout function
  logout: async () => {
    try {
      await api.post('/auth/logout');
      // Server will clear the HttpOnly cookie
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  // Check if user is authenticated
  isAuthenticated: async () => {
    try {
      const response = await api.get('/auth/verify');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
};

// Option 2: Encrypted Session Storage (Medium Security)
export const secureAuthWithEncryption = {
  // Store token in sessionStorage with additional encryption
  setToken: (token) => {
    const encrypted = btoa(JSON.stringify({
      token: token,
      timestamp: Date.now(),
      fingerprint: navigator.userAgent + navigator.language
    }));
    sessionStorage.setItem('_auth', encrypted);
  },

  getToken: () => {
    try {
      const encrypted = sessionStorage.getItem('_auth');
      if (!encrypted) return null;
      
      const decrypted = JSON.parse(atob(encrypted));
      
      // Verify fingerprint
      const currentFingerprint = navigator.userAgent + navigator.language;
      if (decrypted.fingerprint !== currentFingerprint) {
        sessionStorage.removeItem('_auth');
        return null;
      }
      
      // Check if token is too old (e.g., 8 hours)
      const maxAge = 8 * 60 * 60 * 1000; // 8 hours
      if (Date.now() - decrypted.timestamp > maxAge) {
        sessionStorage.removeItem('_auth');
        return null;
      }
      
      return decrypted.token;
    } catch (error) {
      sessionStorage.removeItem('_auth');
      return null;
    }
  },

  clearToken: () => {
    sessionStorage.removeItem('_auth');
  }
};

// Option 3: Memory-only Storage (Highest Security, Lost on Refresh)
class MemoryTokenStore {
  constructor() {
    this.token = null;
    this.expiryTime = null;
  }

  setToken(token, expiryMinutes = 60) {
    this.token = token;
    this.expiryTime = Date.now() + (expiryMinutes * 60 * 1000);
  }

  getToken() {
    if (!this.token || !this.expiryTime) return null;
    
    if (Date.now() > this.expiryTime) {
      this.clearToken();
      return null;
    }
    
    return this.token;
  }

  clearToken() {
    this.token = null;
    this.expiryTime = null;
  }

  isValid() {
    return this.getToken() !== null;
  }
}

export const memoryTokenStore = new MemoryTokenStore();

// Option 4: Service Worker Storage (Advanced) - COMPLETELY HIDDEN
export const serviceWorkerAuth = {
  isReady: false,

  // Register service worker for token management
  init: async () => {
    if ('serviceWorker' in navigator) {
      try {
        // Register the service worker
        const registration = await navigator.serviceWorker.register('/auth-sw.js', {
          scope: '/'
        });

        // Wait for service worker to be ready
        await navigator.serviceWorker.ready;

        // Wait for controller to be available
        if (!navigator.serviceWorker.controller) {
          await new Promise((resolve) => {
            navigator.serviceWorker.addEventListener('controllerchange', resolve, { once: true });
          });
        }

        serviceWorkerAuth.isReady = true;
        console.log('🔒 Service Worker Auth: READY - Tokens will be COMPLETELY HIDDEN');
        return true;
      } catch (error) {
        console.error('🔒 Service Worker registration failed:', error);
        return false;
      }
    }
    console.warn('🔒 Service Worker not supported in this browser');
    return false;
  },

  // Generate browser fingerprint for security
  generateFingerprint: () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Browser fingerprint', 2, 2);

    return btoa(JSON.stringify({
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL()
    }));
  },

  // Send token and user info to service worker (COMPLETELY HIDDEN)
  setToken: async (token, user, expiryMinutes = 60) => {
    if (!serviceWorkerAuth.isReady || !navigator.serviceWorker.controller) {
      console.warn('🔒 Service Worker not ready, falling back to sessionStorage');
      secureAuthWithEncryption.setToken(token);
      return false;
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        if (event.data.success) {
          console.log('🔒 Token stored in Service Worker - COMPLETELY HIDDEN from DevTools');
          resolve(true);
        } else {
          resolve(false);
        }
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'SET_TOKEN',
        token: token,
        user: user,
        expiryMinutes: expiryMinutes,
        fingerprint: serviceWorkerAuth.generateFingerprint()
      }, [channel.port2]);
    });
  },

  // Request token from service worker (HIDDEN)
  getToken: () => {
    if (!serviceWorkerAuth.isReady || !navigator.serviceWorker.controller) {
      return Promise.resolve(null);
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        resolve(event.data.token || null);
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'GET_TOKEN'
      }, [channel.port2]);
    });
  },

  // Get user info from service worker (HIDDEN)
  getUser: () => {
    if (!serviceWorkerAuth.isReady || !navigator.serviceWorker.controller) {
      return Promise.resolve(null);
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        resolve(event.data.user || null);
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'GET_USER'
      }, [channel.port2]);
    });
  },

  // Check authentication status (HIDDEN)
  checkStatus: () => {
    if (!serviceWorkerAuth.isReady || !navigator.serviceWorker.controller) {
      return Promise.resolve({ hasToken: false, isValid: false });
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'CHECK_STATUS'
      }, [channel.port2]);
    });
  },

  // Clear token from service worker (HIDDEN)
  clearToken: () => {
    if (!serviceWorkerAuth.isReady || !navigator.serviceWorker.controller) {
      return Promise.resolve(true);
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        console.log('🔒 Token cleared from Service Worker');
        resolve(event.data.success);
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'CLEAR_TOKEN'
      }, [channel.port2]);
    });
  },

  // Check if service worker is controlling the page
  isControlling: () => {
    return !!(navigator.serviceWorker && navigator.serviceWorker.controller);
  }
};

// Utility to detect if token is visible in DevTools
export const securityCheck = {
  // Check if token is stored in localStorage/sessionStorage
  checkTokenVisibility: () => {
    const checks = {
      localStorage: !!localStorage.getItem('token'),
      sessionStorage: !!sessionStorage.getItem('token'),
      encryptedSession: !!sessionStorage.getItem('_auth'),
      memory: memoryTokenStore.isValid(),
      cookies: document.cookie.includes('auth')
    };

    console.log('🔍 Token Visibility Check:', checks);
    
    if (checks.localStorage || checks.sessionStorage) {
      console.warn('⚠️ Token is visible in browser storage!');
    } else {
      console.log('✅ Token is not visible in browser storage');
    }

    return checks;
  },

  // Generate security report
  generateSecurityReport: () => {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      tokenVisibility: securityCheck.checkTokenVisibility(),
      securityFeatures: {
        httpOnlyCookies: document.cookie.includes('HttpOnly'),
        secureFlag: document.cookie.includes('Secure'),
        sameSite: document.cookie.includes('SameSite'),
        serviceWorker: 'serviceWorker' in navigator,
        webCrypto: 'crypto' in window && 'subtle' in window.crypto
      }
    };

    console.log('📊 Security Report:', report);
    return report;
  }
};

export default {
  secureAuthWithCookies,
  secureAuthWithEncryption,
  memoryTokenStore,
  serviceWorkerAuth,
  securityCheck
};
