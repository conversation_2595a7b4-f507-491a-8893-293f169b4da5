/**
 * Secure Authentication Utilities
 * Uses HttpOnly cookies instead of localStorage for better security
 */

import api from '../config/api';

// Option 1: HttpOnly Cookies (Most Secure)
export const secureAuthWithCookies = {
  // Login function that stores token in HttpOnly cookie
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);
      
      // Server should set HttpOnly cookie automatically
      // No need to store token in localStorage
      
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Logout function
  logout: async () => {
    try {
      await api.post('/auth/logout');
      // Server will clear the HttpOnly cookie
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  // Check if user is authenticated
  isAuthenticated: async () => {
    try {
      const response = await api.get('/auth/verify');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
};

// Option 2: Encrypted Session Storage (Medium Security)
export const secureAuthWithEncryption = {
  // Store token in sessionStorage with additional encryption
  setToken: (token) => {
    const encrypted = btoa(JSON.stringify({
      token: token,
      timestamp: Date.now(),
      fingerprint: navigator.userAgent + navigator.language
    }));
    sessionStorage.setItem('_auth', encrypted);
  },

  getToken: () => {
    try {
      const encrypted = sessionStorage.getItem('_auth');
      if (!encrypted) return null;
      
      const decrypted = JSON.parse(atob(encrypted));
      
      // Verify fingerprint
      const currentFingerprint = navigator.userAgent + navigator.language;
      if (decrypted.fingerprint !== currentFingerprint) {
        sessionStorage.removeItem('_auth');
        return null;
      }
      
      // Check if token is too old (e.g., 8 hours)
      const maxAge = 8 * 60 * 60 * 1000; // 8 hours
      if (Date.now() - decrypted.timestamp > maxAge) {
        sessionStorage.removeItem('_auth');
        return null;
      }
      
      return decrypted.token;
    } catch (error) {
      sessionStorage.removeItem('_auth');
      return null;
    }
  },

  clearToken: () => {
    sessionStorage.removeItem('_auth');
  }
};

// Option 3: Memory-only Storage (Highest Security, Lost on Refresh)
class MemoryTokenStore {
  constructor() {
    this.token = null;
    this.expiryTime = null;
  }

  setToken(token, expiryMinutes = 60) {
    this.token = token;
    this.expiryTime = Date.now() + (expiryMinutes * 60 * 1000);
  }

  getToken() {
    if (!this.token || !this.expiryTime) return null;
    
    if (Date.now() > this.expiryTime) {
      this.clearToken();
      return null;
    }
    
    return this.token;
  }

  clearToken() {
    this.token = null;
    this.expiryTime = null;
  }

  isValid() {
    return this.getToken() !== null;
  }
}

export const memoryTokenStore = new MemoryTokenStore();

// Option 4: Service Worker Storage (Advanced)
export const serviceWorkerAuth = {
  // Register service worker for token management
  init: async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/auth-sw.js');
        console.log('Auth Service Worker registered:', registration);
        return true;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return false;
      }
    }
    return false;
  },

  // Send token to service worker
  setToken: (token) => {
    if (navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'SET_TOKEN',
        token: token
      });
    }
  },

  // Request token from service worker
  getToken: () => {
    return new Promise((resolve) => {
      if (!navigator.serviceWorker.controller) {
        resolve(null);
        return;
      }

      const channel = new MessageChannel();
      channel.port1.onmessage = (event) => {
        resolve(event.data.token || null);
      };

      navigator.serviceWorker.controller.postMessage({
        type: 'GET_TOKEN'
      }, [channel.port2]);
    });
  }
};

// Utility to detect if token is visible in DevTools
export const securityCheck = {
  // Check if token is stored in localStorage/sessionStorage
  checkTokenVisibility: () => {
    const checks = {
      localStorage: !!localStorage.getItem('token'),
      sessionStorage: !!sessionStorage.getItem('token'),
      encryptedSession: !!sessionStorage.getItem('_auth'),
      memory: memoryTokenStore.isValid(),
      cookies: document.cookie.includes('auth')
    };

    console.log('🔍 Token Visibility Check:', checks);
    
    if (checks.localStorage || checks.sessionStorage) {
      console.warn('⚠️ Token is visible in browser storage!');
    } else {
      console.log('✅ Token is not visible in browser storage');
    }

    return checks;
  },

  // Generate security report
  generateSecurityReport: () => {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      tokenVisibility: securityCheck.checkTokenVisibility(),
      securityFeatures: {
        httpOnlyCookies: document.cookie.includes('HttpOnly'),
        secureFlag: document.cookie.includes('Secure'),
        sameSite: document.cookie.includes('SameSite'),
        serviceWorker: 'serviceWorker' in navigator,
        webCrypto: 'crypto' in window && 'subtle' in window.crypto
      }
    };

    console.log('📊 Security Report:', report);
    return report;
  }
};

export default {
  secureAuthWithCookies,
  secureAuthWithEncryption,
  memoryTokenStore,
  serviceWorkerAuth,
  securityCheck
};
