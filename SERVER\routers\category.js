const {
  getAllCategories,
  addCategory,
  editCategory,
  deleteCategory,
  getSublineItems, // ⬅️ Import the new controller
} = require("../controllers/category_controller"); // or update path if in a different controller

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const categoryRouter = Router();

// 🔒 SECURED ROUTES

// Get all categories - Authenticated users only
categoryRouter.get("/categories", ...authenticatedRoute(), getAllCategories);

// ✅ Get subline items dynamically based on category name (from ChartOfAccounts) - Authenticated users only
categoryRouter.get("/categories/subline-items", ...authenticatedRoute(), getSublineItems); // ?category=YourCategory

// Add a new category - Admin only
categoryRouter.post("/categories", ...adminRoute(), addCategory);

// Edit an existing category - Admin only
categoryRouter.put("/categories/:id", ...adminRoute(), editCategory);

// Delete a category - Admin only
categoryRouter.delete("/categories/:id", ...adminRoute(), deleteCategory);

module.exports = categoryRouter;
