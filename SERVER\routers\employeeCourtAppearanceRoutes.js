const {
  getAllAppearances,
  addAppearance,
  editAppearance,
  deleteAppearance,
  getSumOfCourtAppearanceAmount,
} = require("../controllers/employeeCourtAppearanceController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const appearanceRouter = Router();

appearanceRouter.get("/court-appearances", getAllAppearances);
appearanceRouter.post("/court-appearances", checkDueDate, addAppearance);
appearanceRouter.put("/court-appearances/:id", checkDueDate, editAppearance);
appearanceRouter.delete("/court-appearances/:id", checkDueDate, deleteAppearance);
appearanceRouter.get("/court-appearances/sum", getSumOfCourtAppearanceAmount);

module.exports = appearanceRouter;
