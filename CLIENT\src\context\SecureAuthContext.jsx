import React, { createContext, useContext, useEffect, useState } from 'react';
import { serviceWorkerAuth } from '../utils/secureAuth';

const SecureAuthContext = createContext();

export const useSecureAuth = () => {
  const context = useContext(SecureAuthContext);
  if (!context) {
    throw new Error('useSecureAuth must be used within SecureAuthProvider');
  }
  return context;
};

export const SecureAuthProvider = ({ children }) => {
  const [isServiceWorkerReady, setIsServiceWorkerReady] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authStatus, setAuthStatus] = useState({
    hasToken: false,
    isValid: false,
    expiresIn: 0
  });

  // Initialize Service Worker on mount
  useEffect(() => {
    initializeServiceWorker();
  }, []);

  // Check auth status periodically
  useEffect(() => {
    if (isServiceWorkerReady) {
      checkAuthStatus();
      
      // Check every 30 seconds
      const interval = setInterval(checkAuthStatus, 30000);
      return () => clearInterval(interval);
    }
  }, [isServiceWorkerReady]);

  const initializeServiceWorker = async () => {
    try {
      console.log('🔒 Initializing Service Worker Auth...');
      const success = await serviceWorkerAuth.init();
      
      if (success) {
        setIsServiceWorkerReady(true);
        console.log('🔒 Service Worker Auth: READY - Tokens completely hidden!');
        
        // Check if user is already authenticated
        await checkAuthStatus();
      } else {
        console.warn('🔒 Service Worker failed to initialize, using fallback auth');
        setIsServiceWorkerReady(false);
      }
    } catch (error) {
      console.error('🔒 Service Worker initialization error:', error);
      setIsServiceWorkerReady(false);
    } finally {
      setIsLoading(false);
    }
  };

  const checkAuthStatus = async () => {
    try {
      const status = await serviceWorkerAuth.checkStatus();
      setAuthStatus(status);
      
      if (status.isValid && status.user) {
        setCurrentUser(status.user);
      } else {
        setCurrentUser(null);
      }
    } catch (error) {
      console.error('🔒 Auth status check failed:', error);
      setAuthStatus({ hasToken: false, isValid: false, expiresIn: 0 });
      setCurrentUser(null);
    }
  };

  const login = async (credentials) => {
    try {
      setIsLoading(true);
      
      // Make login request (replace with your actual login API)
      const response = await fetch('http://localhost:5005/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const data = await response.json();
      
      if (isServiceWorkerReady) {
        // Store in Service Worker (COMPLETELY HIDDEN)
        const success = await serviceWorkerAuth.setToken(data.token, data.user, 60);
        
        if (success) {
          console.log('🔒 Login successful - Token stored securely in Service Worker');
          setCurrentUser(data.user);
          await checkAuthStatus();
          return { success: true, user: data.user };
        } else {
          throw new Error('Failed to store token securely');
        }
      } else {
        // Fallback to localStorage
        console.warn('🔒 Service Worker not ready, using localStorage fallback');
        localStorage.setItem('token', data.token);
        setCurrentUser(data.user);
        return { success: true, user: data.user };
      }
    } catch (error) {
      console.error('🔒 Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      if (isServiceWorkerReady) {
        // Clear from Service Worker
        await serviceWorkerAuth.clearToken();
        console.log('🔒 Logout: Token cleared from Service Worker');
      }
      
      // Clear fallback storage
      localStorage.removeItem('token');
      
      // Clear state
      setCurrentUser(null);
      setAuthStatus({ hasToken: false, isValid: false, expiresIn: 0 });
      
      console.log('🔒 Logout successful');
      return { success: true };
    } catch (error) {
      console.error('🔒 Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAuthStatus = async () => {
    await checkAuthStatus();
  };

  const getSecurityReport = () => {
    return {
      serviceWorkerReady: isServiceWorkerReady,
      serviceWorkerControlling: serviceWorkerAuth.isControlling(),
      authStatus: authStatus,
      tokenVisibleInDevTools: !!localStorage.getItem('token'),
      securityLevel: isServiceWorkerReady ? 'MAXIMUM (Service Worker)' : 'BASIC (localStorage)',
      recommendation: isServiceWorkerReady 
        ? '✅ Token is COMPLETELY HIDDEN from DevTools' 
        : '⚠️ Token may be visible in DevTools'
    };
  };

  const value = {
    // State
    currentUser,
    isLoading,
    isServiceWorkerReady,
    authStatus,
    
    // Actions
    login,
    logout,
    refreshAuthStatus,
    
    // Utilities
    getSecurityReport,
    
    // Service Worker specific
    serviceWorkerAuth
  };

  return (
    <SecureAuthContext.Provider value={value}>
      {children}
    </SecureAuthContext.Provider>
  );
};
