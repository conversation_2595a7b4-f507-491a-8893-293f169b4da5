const express = require('express');
const salaryRouter = express.Router();
const salaryController = require('../controllers/salaryController');

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// All salary routes require authentication
salaryRouter.get('/salary-grades', ...authenticatedRoute(), salaryController.getSalaryGrades);
salaryRouter.get('/job-grades/:salary_grade', ...authenticatedRoute(), salaryController.getJobGrades);
salaryRouter.get('/steps/:job_grade', ...authenticatedRoute(), salaryController.getSteps);
salaryRouter.get('/rate/:job_grade/:step', ...authenticatedRoute(), salaryController.getRate);
salaryRouter.get('/rate-by-job-grade-and-step/:job_grade/:step', ...authenticatedRoute(), salaryController.getRateByJobGradeAndStep);

module.exports = salaryRouter;
