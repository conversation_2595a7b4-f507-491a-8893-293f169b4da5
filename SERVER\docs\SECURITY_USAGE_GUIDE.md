# Security Middleware Usage Guide

## Quick Start

### Basic Usage

```javascript
// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  superAdminRoute,
  dueDateProtectedRoute,
  regionProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// Apply to routes
router.get('/data', ...authenticatedRoute(), controller.getData);
router.post('/data', ...adminRoute(), controller.createData);
router.delete('/data/:id', ...superAdminRoute(), controller.deleteData);
```

## Security Levels

### 1. Public Routes (No Authentication)
```javascript
// No middleware needed - route is public by default
router.get('/health', (req, res) => res.json({ status: 'OK' }));
```

### 2. Authenticated Routes
```javascript
// Requires valid JWT token
router.get('/profile', ...authenticatedRoute(), userController.getProfile);
router.get('/dashboard', ...authenticatedRoute(), dashboardController.getStats);
```

### 3. Admin Routes
```javascript
// Requires SUPER ADMIN, BUDGET MANAGER, or BUDGET OFFICER role
router.post('/users', ...adminRoute(), userController.createUser);
router.put('/settings/:id', ...adminRoute(), settingsController.updateSettings);
router.delete('/data/:id', ...adminRoute(), dataController.deleteData);
```

### 4. Super Admin Only Routes
```javascript
// Requires SUPER ADMIN role only
router.get('/system/logs', ...superAdminRoute(), systemController.getLogs);
router.post('/users/admin', ...superAdminRoute(), userController.createAdmin);
```

## Special Protection Middleware

### Due Date Protection
Prevents modifications after fiscal year deadline:

```javascript
// Admin access + due date validation
router.post('/proposals', ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), proposalController.create);
router.put('/personnel/:id', ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), personnelController.update);
```

### Region Access Protection
Ensures users can only access their assigned regions:

```javascript
// Authentication + region access validation
router.get('/regional-data', ...regionProtectedRoute(PERMISSION_LEVELS.AUTHENTICATED), dataController.getRegionalData);

// Admin access + region validation
router.post('/regional-proposals', ...regionProtectedRoute(PERMISSION_LEVELS.ADMIN), proposalController.createRegional);
```

### Combined Protection
```javascript
// Admin access + due date + region validation
router.post('/submit-proposal', 
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  checkRegionAccess(),
  proposalController.submit
);
```

## Error Handling

The security middleware returns standardized error responses:

### 401 Unauthorized (No/Invalid Token)
```json
{
  "error": "Authentication required",
  "message": "Please log in to access this resource"
}
```

### 403 Forbidden (Insufficient Permissions)
```json
{
  "error": "Insufficient permissions",
  "message": "You do not have permission to access this resource",
  "required": "ADMIN",
  "userRoles": ["USER"]
}
```

### 403 Due Date Restriction
```json
{
  "error": "Submission deadline passed",
  "message": "Submissions are closed. Deadline was 12/31/2023.",
  "dueDate": "2023-12-31T23:59:59.999Z"
}
```

### 403 Region Access Denied
```json
{
  "error": "Region access denied",
  "message": "You don't have permission to access data for region: NCR"
}
```

## Frontend Integration

### Axios Interceptor Setup
```javascript
// CLIENT/src/config/api.js
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login
      window.location.href = '/login';
    } else if (error.response?.status === 403) {
      // Show access denied message
      toast.error(error.response.data.message);
    }
    return Promise.reject(error);
  }
);
```

### React Route Protection
```javascript
// CLIENT/src/App.jsx
<Route 
  path="/admin-panel" 
  element={
    <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET MANAGER"]}>
      <AdminPanel />
    </ProtectedRoute>
  } 
/>
```

## Testing Security

### Manual Testing
```bash
# Test without token (should fail)
curl http://localhost:3001/api/proposals

# Test with valid token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:3001/api/proposals

# Test admin endpoint with user token (should fail)
curl -H "Authorization: Bearer USER_JWT_TOKEN" -X POST http://localhost:3001/api/proposals
```

### Automated Testing
```javascript
// Example test
describe('Security Middleware', () => {
  it('should require authentication for protected routes', async () => {
    const response = await request(app)
      .get('/api/proposals')
      .expect(401);
    
    expect(response.body.error).toBe('Authentication required');
  });
  
  it('should allow admin access to admin routes', async () => {
    const response = await request(app)
      .post('/api/proposals')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(proposalData)
      .expect(201);
  });
});
```

## Configuration

### Adding New Routes
1. Add route permission to `SERVER/config/rolePermissions.js`:
```javascript
const ROUTE_PERMISSIONS = {
  // ... existing routes
  'GET:/new-endpoint': PERMISSION_LEVELS.AUTHENTICATED,
  'POST:/new-endpoint': PERMISSION_LEVELS.ADMIN,
};
```

2. Apply middleware to route:
```javascript
router.get('/new-endpoint', ...authenticatedRoute(), controller.method);
router.post('/new-endpoint', ...adminRoute(), controller.method);
```

### Custom Permission Levels
```javascript
// Add to rolePermissions.js
const CUSTOM_PERMISSION = 'CUSTOM_PERMISSION';

const ROLE_PERMISSIONS = {
  [ROLES.CUSTOM_ROLE]: [
    PERMISSION_LEVELS.PUBLIC,
    PERMISSION_LEVELS.AUTHENTICATED,
    CUSTOM_PERMISSION
  ]
};
```

## Best Practices

1. **Always use the spread operator** when applying middleware:
   ```javascript
   // ✅ Correct
   router.get('/data', ...authenticatedRoute(), controller.getData);
   
   // ❌ Incorrect
   router.get('/data', authenticatedRoute(), controller.getData);
   ```

2. **Order middleware correctly**:
   ```javascript
   // ✅ Correct order
   router.post('/data', 
     ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
     checkRegionAccess(),
     controller.createData
   );
   ```

3. **Use appropriate security levels**:
   - GET operations: Usually `authenticatedRoute()`
   - POST/PUT/DELETE: Usually `adminRoute()` or higher
   - System operations: `superAdminRoute()`

4. **Handle errors gracefully** in frontend
5. **Test security thoroughly** before deployment
6. **Keep permissions centralized** in configuration files

## Troubleshooting

### Common Issues

1. **"Cannot read property 'Roles' of undefined"**
   - Check if `req.user` is set by authentication middleware
   - Ensure `checkToken` middleware runs before authorization

2. **"Access denied" for valid users**
   - Verify user roles in database match expected roles
   - Check role spelling and case sensitivity

3. **Middleware not applying**
   - Ensure spread operator (`...`) is used
   - Check middleware import paths

### Debug Mode
```javascript
// Add to route for debugging
router.get('/debug', (req, res, next) => {
  console.log('User:', req.user);
  console.log('Roles:', req.user?.Roles);
  next();
}, ...authenticatedRoute(), controller.method);
```
