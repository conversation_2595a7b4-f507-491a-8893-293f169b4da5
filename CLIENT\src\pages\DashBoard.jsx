import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Divider,
  Stack,
  Container,
  IconButton,
  Chip,
  Avatar,
  LinearProgress,
  Fade,
  Zoom,
  Paper,
  Button,
  Tooltip,
  useTheme,
  alpha
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import api from "../config/api";
import DashboardHeader from "../global/components/DashboardHeader";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ListAltIcon from "@mui/icons-material/ListAlt";
import AssessmentIcon from "@mui/icons-material/Assessment";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import Brightness4Icon from "@mui/icons-material/Brightness4";
import Brightness7Icon from "@mui/icons-material/Brightness7";
import PendingActionsIcon from "@mui/icons-material/PendingActions";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import NotificationsIcon from "@mui/icons-material/Notifications";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import TimelineIcon from "@mui/icons-material/Timeline";
import { useNavigate } from "react-router-dom";
import { useThemeContext } from "../context/ThemeContext";
import { useUser } from "../context/UserContext";
import DebugAuth from "../components/DebugAuth"; // Import DebugAuth component

const HomePage = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { mode, toggleTheme } = useThemeContext();
  const { currentUser } = useUser();
  const isDarkMode = mode === 'dark';
  const [animationDelay, setAnimationDelay] = useState(0);

  const { data: stats, isLoading } = useQuery({
    queryKey: ["dashboardStats"],
    queryFn: async () => {
      const res = await api.get("/stats/overview");
      return res.data;
    },
  });

  // Fetch active settings for submission deadline
  const { data: activeSettings, isLoading: settingsLoading } = useQuery({
    queryKey: ["activeSettings"],
    queryFn: async () => {
      const res = await api.get("/settings/active");
      return res.data;
    },
  });

  // Fallback values if data is not yet loaded
  const totalProposals = stats?.totalProposals || 0;
  const pendingProposals = stats?.pendingProposals || 0;
  const approvedProposals = stats?.approvedProposals || 0;
  const rejectedProposals = stats?.rejectedProposals || 0;

  // Animation effect
  useEffect(() => {
    setAnimationDelay(100);
  }, []);

  // Calculate completion percentage
  const completionRate = totalProposals > 0 ? ((approvedProposals / totalProposals) * 100).toFixed(1) : 0;

  // Format submission deadline
  const formatSubmissionDeadline = () => {
    if (!activeSettings?.dueDate) return "To Be Determined";

    const dueDate = new Date(activeSettings.dueDate);
    const now = new Date();
    const timeDiff = dueDate - now;

    // Format the date
    const formattedDate = dueDate.toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });

    // Calculate days remaining
    const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    if (daysRemaining < 0) {
      return `${formattedDate} (Deadline Passed)`;
    } else if (daysRemaining === 0) {
      return `${formattedDate} (Today!)`;
    } else if (daysRemaining === 1) {
      return `${formattedDate} (Tomorrow)`;
    } else {
      return `${formattedDate} (${daysRemaining} days remaining)`;
    }
  };

  // Get deadline status for styling
  const getDeadlineStatus = () => {
    if (!activeSettings?.dueDate) return 'info';

    const dueDate = new Date(activeSettings.dueDate);
    const now = new Date();
    const daysRemaining = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

    if (daysRemaining < 0) return 'error'; // Passed
    if (daysRemaining <= 3) return 'error'; // Critical
    if (daysRemaining <= 7) return 'warning'; // Warning
    return 'success'; // Good
  };

  const handleCreateProposal = () => {
    navigate("/proposals/create");
  };

  const handleViewProposals = () => {
    navigate("/proposals");
  };

  const handleGenerateReports = () => {
    navigate("/reports");
  };

  return (
    <>
      <DashboardHeader
        title="Dashboard"
        description="Overview of your budget proposals and system status"
        action={
          <Stack direction="row" spacing={2} alignItems="center">
            <Chip
              label={`${isDarkMode ? '🌙 Dark' : '☀️ Light'} Mode`}
              variant="outlined"
              size="small"
              sx={{
                color: 'inherit',
                borderColor: alpha('#fff', 0.4),
                fontWeight: 600,
                fontSize: '0.75rem',
                '&:hover': {
                  borderColor: alpha('#fff', 0.6),
                  bgcolor: alpha('#fff', 0.1)
                }
              }}
            />
            <Tooltip title={`Click to switch to ${isDarkMode ? 'Light' : 'Dark'} Mode`}>
              <IconButton
                onClick={toggleTheme}
                color="inherit"
                sx={{
                  p: 1.5,
                  border: `2px solid ${alpha('#fff', 0.3)}`,
                  borderRadius: 2,
                  bgcolor: alpha('#fff', 0.1),
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'rotate(180deg) scale(1.15)',
                    bgcolor: alpha('#fff', 0.2),
                    borderColor: alpha('#fff', 0.5),
                    boxShadow: `0 6px 16px ${alpha('#000', 0.3)}`
                  },
                  '&:active': {
                    transform: 'rotate(180deg) scale(1.05)',
                  }
                }}
              >
                {isDarkMode ? <Brightness7Icon sx={{ fontSize: 24 }} /> : <Brightness4Icon sx={{ fontSize: 24 }} />}
              </IconButton>
            </Tooltip>
          </Stack>
        }
      />

      <Container maxWidth="xl">
        {/* Enhanced Welcome Card */}
        <Fade in timeout={800}>
          <Card
            elevation={isDarkMode ? 8 : 6}
            sx={{
              mt: 3,
              mb: 4,
              borderRadius: 3,
              background: isDarkMode
                ? "linear-gradient(135deg, #1a237e 0%, #283593 50%, #3949ab 100%)"
                : "linear-gradient(135deg, rgba(38, 69, 36, 0.9) 0%, rgba(55, 94, 56, 0.8) 50%, rgba(76, 175, 80, 0.7) 100%)",
              color: "white",
              position: "relative",
              overflow: "hidden",
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: isDarkMode
                  ? 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)'
                  : 'radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
                pointerEvents: 'none'
              }
            }}
          >
            <CardContent sx={{ py: 4, px: 4, position: 'relative', zIndex: 1 }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h4" fontWeight="bold" gutterBottom sx={{ mb: 2 }}>
                    Welcome back, {currentUser?.FirstName || 'User'}! 👋
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9, mb: 1 }}>
                    Budget Proposal Management System
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.8, maxWidth: '600px' }}>
                    Manage and track all your budget proposals in one place. Create new proposals, monitor their status, and generate comprehensive reports.
                  </Typography>
                </Box>
                <Box sx={{ display: { xs: 'none', md: 'block' } }}>
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      bgcolor: alpha('#fff', 0.2),
                      border: '3px solid rgba(255,255,255,0.3)'
                    }}
                  >
                    <AssessmentIcon sx={{ fontSize: 40 }} />
                  </Avatar>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Fade>

        {/* Enhanced Stats Cards */}
        <Box sx={{ mb: 4 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
            <Typography variant="h5" fontWeight="bold">
              📊 Proposal Statistics
            </Typography>
            <Chip
              label={`${completionRate}% Completion Rate`}
              color="success"
              variant="outlined"
              icon={<TimelineIcon />}
            />
          </Stack>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Zoom in timeout={600}>
                <Card
                  elevation={isDarkMode ? 8 : 4}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)'
                      : 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                    border: isDarkMode ? '1px solid rgba(59, 130, 246, 0.3)' : '1px solid rgba(14, 165, 233, 0.2)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? '0 20px 40px rgba(59, 130, 246, 0.3)'
                        : '0 20px 40px rgba(14, 165, 233, 0.2)'
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: "center", p: 3 }}>
                    <Box sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha('#3b82f6', 0.1),
                      mb: 2
                    }}>
                      <TrendingUpIcon sx={{ fontSize: 32, color: '#3b82f6' }} />
                    </Box>
                    <Typography variant="h6" fontWeight="600" gutterBottom color="text.primary">
                      Total Proposals
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" sx={{ color: '#3b82f6' }}>
                      {isLoading ? "..." : totalProposals}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={100}
                      sx={{
                        mt: 2,
                        height: 6,
                        borderRadius: 3,
                        bgcolor: alpha('#3b82f6', 0.1),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: '#3b82f6'
                        }
                      }}
                    />
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Zoom in timeout={800}>
                <Card
                  elevation={isDarkMode ? 8 : 4}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #d97706 0%, #f59e0b 100%)'
                      : 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
                    border: isDarkMode ? '1px solid rgba(245, 158, 11, 0.3)' : '1px solid rgba(217, 119, 6, 0.2)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? '0 20px 40px rgba(245, 158, 11, 0.3)'
                        : '0 20px 40px rgba(217, 119, 6, 0.2)'
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: "center", p: 3 }}>
                    <Box sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha('#f59e0b', 0.1),
                      mb: 2
                    }}>
                      <PendingActionsIcon sx={{ fontSize: 32, color: '#f59e0b' }} />
                    </Box>
                    <Typography variant="h6" fontWeight="600" gutterBottom color="text.primary">
                      Pending
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" sx={{ color: '#f59e0b' }}>
                      {isLoading ? "..." : pendingProposals}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={totalProposals > 0 ? (pendingProposals / totalProposals) * 100 : 0}
                      sx={{
                        mt: 2,
                        height: 6,
                        borderRadius: 3,
                        bgcolor: alpha('#f59e0b', 0.1),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: '#f59e0b'
                        }
                      }}
                    />
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Zoom in timeout={1000}>
                <Card
                  elevation={isDarkMode ? 8 : 4}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)'
                      : 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
                    border: isDarkMode ? '1px solid rgba(16, 185, 129, 0.3)' : '1px solid rgba(5, 150, 105, 0.2)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? '0 20px 40px rgba(16, 185, 129, 0.3)'
                        : '0 20px 40px rgba(5, 150, 105, 0.2)'
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: "center", p: 3 }}>
                    <Box sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha('#10b981', 0.1),
                      mb: 2
                    }}>
                      <CheckCircleIcon sx={{ fontSize: 32, color: '#10b981' }} />
                    </Box>
                    <Typography variant="h6" fontWeight="600" gutterBottom color="text.primary">
                      Approved
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" sx={{ color: '#10b981' }}>
                      {isLoading ? "..." : approvedProposals}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={totalProposals > 0 ? (approvedProposals / totalProposals) * 100 : 0}
                      sx={{
                        mt: 2,
                        height: 6,
                        borderRadius: 3,
                        bgcolor: alpha('#10b981', 0.1),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: '#10b981'
                        }
                      }}
                    />
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Zoom in timeout={1200}>
                <Card
                  elevation={isDarkMode ? 8 : 4}
                  sx={{
                    height: '100%',
                    borderRadius: 3,
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)'
                      : 'linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)',
                    border: isDarkMode ? '1px solid rgba(239, 68, 68, 0.3)' : '1px solid rgba(220, 38, 38, 0.2)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? '0 20px 40px rgba(239, 68, 68, 0.3)'
                        : '0 20px 40px rgba(220, 38, 38, 0.2)'
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: "center", p: 3 }}>
                    <Box sx={{
                      display: 'inline-flex',
                      p: 2,
                      borderRadius: '50%',
                      bgcolor: alpha('#ef4444', 0.1),
                      mb: 2
                    }}>
                      <CancelIcon sx={{ fontSize: 32, color: '#ef4444' }} />
                    </Box>
                    <Typography variant="h6" fontWeight="600" gutterBottom color="text.primary">
                      Rejected
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" sx={{ color: '#ef4444' }}>
                      {isLoading ? "..." : rejectedProposals}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={totalProposals > 0 ? (rejectedProposals / totalProposals) * 100 : 0}
                      sx={{
                        mt: 2,
                        height: 6,
                        borderRadius: 3,
                        bgcolor: alpha('#ef4444', 0.1),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: '#ef4444'
                        }
                      }}
                    />
                  </CardContent>
                </Card>
              </Zoom>
            </Grid>
          </Grid>
        </Box>

        {/* Enhanced Quick Actions */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            🚀 Quick Actions
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Fade in timeout={1000}>
                <Card
                  elevation={isDarkMode ? 6 : 3}
                  sx={{
                    borderRadius: 3,
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
                      : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? `0 20px 40px ${alpha(theme.palette.primary.main, 0.2)}`
                        : `0 20px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`
                    },
                    height: '100%'
                  }}
                  onClick={handleCreateProposal}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Stack spacing={3}>
                      <Box sx={{
                        display: 'inline-flex',
                        p: 2,
                        borderRadius: 3,
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        alignSelf: 'flex-start'
                      }}>
                        <AddCircleOutlineIcon sx={{ fontSize: 32, color: 'primary.main' }} />
                      </Box>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Create New Proposal
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                          Start a new budget proposal submission with our intuitive step-by-step wizard.
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        color="primary"
                        sx={{
                          alignSelf: 'flex-start',
                          borderRadius: 2,
                          textTransform: 'none',
                          fontWeight: 600
                        }}
                      >
                        Get Started
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>

            <Grid item xs={12} md={4}>
              <Fade in timeout={1200}>
                <Card
                  elevation={isDarkMode ? 6 : 3}
                  sx={{
                    borderRadius: 3,
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
                      : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                    border: `1px solid ${alpha(theme.palette.secondary.main, 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? `0 20px 40px ${alpha(theme.palette.secondary.main, 0.2)}`
                        : `0 20px 40px ${alpha(theme.palette.secondary.main, 0.15)}`,
                      border: `1px solid ${alpha(theme.palette.secondary.main, 0.3)}`
                    },
                    height: '100%'
                  }}
                  onClick={handleViewProposals}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Stack spacing={3}>
                      <Box sx={{
                        display: 'inline-flex',
                        p: 2,
                        borderRadius: 3,
                        bgcolor: alpha(theme.palette.secondary.main, 0.1),
                        alignSelf: 'flex-start'
                      }}>
                        <ListAltIcon sx={{ fontSize: 32, color: 'secondary.main' }} />
                      </Box>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          View All Proposals
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                          Browse, filter, and manage all your submitted budget proposals efficiently.
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        color="secondary"
                        sx={{
                          alignSelf: 'flex-start',
                          borderRadius: 2,
                          textTransform: 'none',
                          fontWeight: 600
                        }}
                      >
                        View Proposals
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>

            <Grid item xs={12} md={4}>
              <Fade in timeout={1400}>
                <Card
                  elevation={isDarkMode ? 6 : 3}
                  sx={{
                    borderRadius: 3,
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    background: isDarkMode
                      ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
                      : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                    border: `1px solid ${alpha('#10b981', 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.02)',
                      boxShadow: isDarkMode
                        ? `0 20px 40px ${alpha('#10b981', 0.2)}`
                        : `0 20px 40px ${alpha('#10b981', 0.15)}`,
                      border: `1px solid ${alpha('#10b981', 0.3)}`
                    },
                    height: '100%'
                  }}
                  onClick={handleGenerateReports}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Stack spacing={3}>
                      <Box sx={{
                        display: 'inline-flex',
                        p: 2,
                        borderRadius: 3,
                        bgcolor: alpha('#10b981', 0.1),
                        alignSelf: 'flex-start'
                      }}>
                        <AssessmentIcon sx={{ fontSize: 32, color: '#10b981' }} />
                      </Box>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          Generate Reports
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                          Create customized reports and export comprehensive data for analysis.
                        </Typography>
                      </Box>
                      <Button
                        variant="outlined"
                        sx={{
                          alignSelf: 'flex-start',
                          borderRadius: 2,
                          textTransform: 'none',
                          fontWeight: 600,
                          color: '#10b981',
                          borderColor: '#10b981',
                          '&:hover': {
                            borderColor: '#059669',
                            bgcolor: alpha('#10b981', 0.1)
                          }
                        }}
                      >
                        Generate Reports
                      </Button>
                    </Stack>
                  </CardContent>
                </Card>
              </Fade>
            </Grid>
          </Grid>
        </Box>

        {/* Enhanced Announcements & Deadlines */}
        <Fade in timeout={1600}>
          <Paper
            elevation={isDarkMode ? 8 : 4}
            sx={{
              borderRadius: 3,
              mb: 4,
              background: isDarkMode
                ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
                : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
              border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
              overflow: 'hidden'
            }}
          >
            <Box sx={{
              background: isDarkMode
                ? 'linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%)'
                : 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
              p: 3,
              color: 'white'
            }}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <NotificationsIcon sx={{ fontSize: 28 }} />
                <Typography variant="h5" fontWeight="bold">
                  📢 Announcements & Important Dates
                </Typography>
              </Stack>
            </Box>

            <CardContent sx={{ p: 4 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Stack spacing={3}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(getDeadlineStatus() === 'error' ? '#ef4444' : getDeadlineStatus() === 'warning' ? '#f59e0b' : '#10b981', 0.1),
                      border: `1px solid ${alpha(getDeadlineStatus() === 'error' ? '#ef4444' : getDeadlineStatus() === 'warning' ? '#f59e0b' : '#10b981', 0.2)}`
                    }}>
                      <Box sx={{
                        display: 'inline-flex',
                        p: 1,
                        borderRadius: '50%',
                        bgcolor: getDeadlineStatus() === 'error' ? '#ef4444' : getDeadlineStatus() === 'warning' ? '#f59e0b' : '#10b981',
                        mr: 2
                      }}>
                        <CalendarTodayIcon sx={{ fontSize: 16, color: 'white' }} />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="subtitle2"
                          fontWeight="bold"
                          color={getDeadlineStatus() === 'error' ? '#ef4444' : getDeadlineStatus() === 'warning' ? '#f59e0b' : '#10b981'}
                        >
                          Submission Deadline
                        </Typography>
                        <Typography variant="body2" fontWeight="medium" sx={{ wordBreak: 'break-word' }}>
                          {settingsLoading ? "Loading..." : formatSubmissionDeadline()}
                        </Typography>
                        {activeSettings?.fiscalYear && (
                          <Typography variant="caption" color="text.secondary">
                            Fiscal Year: {activeSettings.fiscalYear}
                          </Typography>
                        )}
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', p: 2, borderRadius: 2, bgcolor: alpha('#3b82f6', 0.1) }}>
                      <Box sx={{
                        display: 'inline-flex',
                        p: 1,
                        borderRadius: '50%',
                        bgcolor: '#3b82f6',
                        mr: 2
                      }}>
                        <NotificationsIcon sx={{ fontSize: 16, color: 'white' }} />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold" color="#3b82f6">
                          Budget Type
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {settingsLoading ? "Loading..." : (activeSettings?.budgetType || "Not Set")}
                        </Typography>
                        {activeSettings?.startDate && (
                          <Typography variant="caption" color="text.secondary">
                            Started: {new Date(activeSettings.startDate).toLocaleDateString('en-PH')}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Stack>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{
                    p: 3,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.info.main, 0.05),
                    border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                    height: '100%'
                  }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom color="info.main">
                      📊 Quick Stats
                    </Typography>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Active Users:</Typography>
                        <Chip label="24" size="small" color="info" />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">System Uptime:</Typography>
                        <Chip label="99.9%" size="small" color="success" />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Last Backup:</Typography>
                        <Chip label="2 hours ago" size="small" color="default" />
                      </Box>
                    </Stack>
                  </Box>
                </Grid>
              </Grid>

              {stats?.settings?.additionalAnnouncements?.length > 0 && (
                <Box sx={{ mt: 3, pt: 3, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Additional Announcements
                  </Typography>
                  <Stack spacing={2}>
                    {stats.settings.additionalAnnouncements.map((announcement, index) => (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'center', p: 2, borderRadius: 2, bgcolor: alpha(theme.palette.info.main, 0.05) }}>
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'info.main',
                          mr: 2
                        }} />
                        <Typography variant="body2" fontWeight="medium">
                          {announcement}
                        </Typography>
                      </Box>
                    ))}
                  </Stack>
                </Box>
              )}
            </CardContent>
          </Paper>
        </Fade>

        {/* 🔒 Service Worker Debug Panel - Remove in production */}
        <Box sx={{ mt: 4, mb: 2 }}>
          <Paper
            elevation={isDarkMode ? 6 : 3}
            sx={{
              p: 3,
              borderRadius: 3,
              background: isDarkMode
                ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
                : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
            }}
          >
            <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: 'warning.main' }}>
              🔒 Security Debug Panel
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
              Monitor Service Worker authentication status and token security
            </Typography>
            <DebugAuth />
          </Paper>
        </Box>
      </Container>
    </>
  );
};

export default HomePage;
