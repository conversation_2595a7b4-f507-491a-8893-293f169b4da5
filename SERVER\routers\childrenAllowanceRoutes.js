const Router = require("express").Router;
const {
  getAllChildrenAllowances,
  getChildrenAllowance,
  createChildrenAllowance,
  updateChildrenAllowance,
  deleteChildrenAllowance,
  syncAllChildrenAllowances,
} = require("../controllers/childrenAllowanceController");

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const childrenAllowanceRouter = Router();

// 🔒 SECURED ROUTES

// List all children allowance records - Authenticated users only
childrenAllowanceRouter.get("/children-allowance", ...authenticatedRoute(), getAllChildrenAllowances);

// Retrieve a single children allowance record - Authenticated users only
childrenAllowanceRouter.get("/children-allowance/:id", ...authenticatedRoute(), getChildrenAllowance);

// Create a new children allowance record - Admin only with due date check
childrenAllowanceRouter.post(
  "/children-allowance",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  createChildrenAllowance
);

// Update an existing children allowance record - Admin only with due date check
childrenAllowanceRouter.put(
  "/children-allowance/:id",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  updateChildrenAllowance
);

// Delete a children allowance record - Admin only with due date check
childrenAllowanceRouter.delete(
  "/children-allowance/:id",
  ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
  deleteChildrenAllowance
);

// Synchronize all children allowances with personnel services - Admin only
childrenAllowanceRouter.post(
  "/children-allowance/sync-all",
  ...adminRoute(),
  syncAllChildrenAllowances
);

module.exports = childrenAllowanceRouter;