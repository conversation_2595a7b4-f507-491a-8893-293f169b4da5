const {
  createOvertimePay,
  getAllOvertimePays,
  updateOvertimePay,
  deleteOvertimePay,
  getSumOfOvertimeAmount,
} = require("../controllers/overtimePayController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const overtimeRouter = Router();

// 🔒 SECURED ROUTES

// Get all overtime pays - Authenticated users only
overtimeRouter.get("/overtime-pay", ...authenticatedRoute(), getAllOvertimePays);

// Get sum of overtime amounts - Authenticated users only
overtimeRouter.get("/overtime-pay/sum", ...authenticatedRoute(), getSumOfOvertimeAmount);

// Create overtime pay - Admin only with due date check
overtimeRouter.post("/overtime-pay", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), createOvertimePay);

// Update overtime pay - Admin only with due date check
overtimeRouter.put("/overtime-pay/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateOvertimePay);

// Delete overtime pay - Admin only with due date check
overtimeRouter.delete("/overtime-pay/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteOvertimePay);

module.exports = overtimeRouter;
