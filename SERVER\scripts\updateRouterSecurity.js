/**
 * <PERSON><PERSON><PERSON> to update remaining router files with security middleware
 * This script will help identify which routers still need security updates
 */

const fs = require('fs');
const path = require('path');

const routersDir = path.join(__dirname, '../routers');

// List of routers that have been manually updated
const updatedRouters = [
  'proposalRoutes.js',
  'personnelServices_router.js', 
  'mooeRoutes.js',
  'RATARoutes.js',
  'retireeRoutes.js',
  'loyaltyPayRoutes.js',
  'COSPersonnel_router.js',
  'setupRoutes.js',
  'capitalOutlayRouter.js',
  'category.js',
  'UserRegionAssignmentRoutes.js', // Already secured
  'budgetarySupportRoutes.js' // Already secured
];

// Security middleware import template
const securityImportTemplate = `
// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  superAdminRoute,
  dueDateProtectedRoute,
  regionProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');
`;

// Common route patterns and their security levels
const routePatterns = {
  // GET routes - usually authenticated
  'get': {
    default: 'authenticatedRoute()',
    patterns: {
      '/stats': 'authenticatedRoute()',
      '/reports': 'authenticatedRoute()',
      '/summary': 'authenticatedRoute()',
      '/list': 'authenticatedRoute()',
      '/total': 'authenticatedRoute()',
      '/grand': 'authenticatedRoute()'
    }
  },
  // POST routes - usually admin
  'post': {
    default: 'adminRoute()',
    patterns: {
      '/bulk-add': 'dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN)',
      '/upload': 'adminRoute()',
      '/save': 'adminRoute()',
      '/create': 'adminRoute()'
    }
  },
  // PUT routes - usually admin
  'put': {
    default: 'adminRoute()',
    patterns: {
      '/:id': 'dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN)'
    }
  },
  // DELETE routes - usually admin
  'delete': {
    default: 'adminRoute()',
    patterns: {
      '/:id': 'dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN)',
      '/deleteAll': 'adminRoute()'
    }
  }
};

function analyzeRouterFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  
  console.log(`\n=== Analyzing ${fileName} ===`);
  
  // Check if already has security middleware
  if (content.includes('securityMiddleware') || content.includes('checkToken')) {
    console.log('✅ Already has security middleware');
    return;
  }
  
  // Find route definitions
  const routeRegex = /router\.(get|post|put|delete)\s*\(\s*["']([^"']+)["']/g;
  const routes = [];
  let match;
  
  while ((match = routeRegex.exec(content)) !== null) {
    routes.push({
      method: match[1],
      path: match[2],
      line: content.substring(0, match.index).split('\n').length
    });
  }
  
  if (routes.length === 0) {
    console.log('❌ No routes found');
    return;
  }
  
  console.log('📋 Found routes:');
  routes.forEach(route => {
    console.log(`  ${route.method.toUpperCase()} ${route.path} (line ${route.line})`);
  });
  
  // Suggest security levels
  console.log('\n💡 Suggested security:');
  routes.forEach(route => {
    const methodPatterns = routePatterns[route.method];
    let suggestion = methodPatterns.default;
    
    // Check for specific patterns
    for (const [pattern, security] of Object.entries(methodPatterns.patterns)) {
      if (route.path.includes(pattern)) {
        suggestion = security;
        break;
      }
    }
    
    console.log(`  ${route.method.toUpperCase()} ${route.path} -> ...${suggestion}`);
  });
}

function scanAllRouters() {
  console.log('🔍 Scanning router files for security updates...\n');
  
  const files = fs.readdirSync(routersDir);
  const routerFiles = files.filter(file => 
    file.endsWith('.js') && 
    !file.startsWith('test') &&
    !updatedRouters.includes(file)
  );
  
  console.log(`Found ${routerFiles.length} router files to analyze:`);
  routerFiles.forEach(file => console.log(`  - ${file}`));
  
  routerFiles.forEach(file => {
    const filePath = path.join(routersDir, file);
    analyzeRouterFile(filePath);
  });
  
  console.log('\n📝 Summary:');
  console.log(`✅ Already updated: ${updatedRouters.length} files`);
  console.log(`⏳ Need updates: ${routerFiles.length} files`);
  console.log(`📁 Total routers: ${files.filter(f => f.endsWith('.js')).length} files`);
}

// Run the analysis
if (require.main === module) {
  scanAllRouters();
}

module.exports = {
  scanAllRouters,
  analyzeRouterFile,
  routePatterns,
  securityImportTemplate
};
