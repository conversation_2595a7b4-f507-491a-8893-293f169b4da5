import CryptoJS from "crypto-js";
import env from "./env";

/**
 * Debug utility to help diagnose authentication issues
 */
export const debugAuth = () => {
  console.log("🔍 Authentication Debug Information");
  console.log("=====================================");
  
  // Check if token exists in localStorage
  const encryptedToken = localStorage.getItem("token");
  console.log("1. Token in localStorage:", encryptedToken ? "✅ Present" : "❌ Missing");
  
  if (encryptedToken) {
    try {
      // Try to decrypt token
      const bytes = CryptoJS.AES.decrypt(encryptedToken, env("SECRET_KEY"));
      const decryptedToken = bytes.toString(CryptoJS.enc.Utf8);
      
      if (decryptedToken) {
        console.log("2. Token decryption:", "✅ Success");
        console.log("3. Token length:", decryptedToken.length);
        
        // Try to parse JWT payload (basic check)
        try {
          const parts = decryptedToken.split('.');
          if (parts.length === 3) {
            const payload = JSON.parse(atob(parts[1]));
            console.log("4. JWT structure:", "✅ Valid");
            console.log("5. JWT payload:", payload);
            console.log("6. Token expiry:", new Date(payload.exp * 1000));
            console.log("7. Is expired:", Date.now() > payload.exp * 1000 ? "❌ Yes" : "✅ No");
          } else {
            console.log("4. JWT structure:", "❌ Invalid");
          }
        } catch (jwtError) {
          console.log("4. JWT parsing error:", jwtError.message);
        }
      } else {
        console.log("2. Token decryption:", "❌ Failed - empty result");
      }
    } catch (decryptError) {
      console.log("2. Token decryption:", "❌ Failed -", decryptError.message);
    }
  }
  
  // Check environment variables
  console.log("\n🔧 Environment Variables:");
  console.log("SERVER_URL:", env("SERVER_URL"));
  console.log("AUTH_SERVER_URL:", env("AUTH_SERVER_URL"));
  console.log("SECRET_KEY:", env("SECRET_KEY") ? "✅ Present" : "❌ Missing");
  
  console.log("\n=====================================");
};

/**
 * Test API endpoint with current authentication
 */
export const testApiEndpoint = async (endpoint = '/proposals') => {
  console.log(`🧪 Testing API endpoint: ${endpoint}`);
  console.log("=====================================");
  
  try {
    const api = (await import('../config/api')).default;
    const response = await api.get(endpoint);
    console.log("✅ API call successful");
    console.log("Response status:", response.status);
    console.log("Response data:", response.data);
  } catch (error) {
    console.log("❌ API call failed");
    console.log("Error status:", error.response?.status);
    console.log("Error message:", error.response?.data?.message || error.message);
    console.log("Full error:", error.response?.data);
  }
  
  console.log("=====================================");
};

/**
 * Check user context
 */
export const debugUserContext = (currentUser) => {
  console.log("👤 User Context Debug");
  console.log("=====================================");
  
  if (currentUser) {
    console.log("✅ User is logged in");
    console.log("User ID:", currentUser.id);
    console.log("User name:", currentUser.name);
    console.log("User email:", currentUser.email);
    console.log("User roles:", currentUser.Roles || currentUser.roles);
  } else {
    console.log("❌ No user in context");
  }
  
  console.log("=====================================");
};

/**
 * Comprehensive debug function
 */
export const fullAuthDebug = async (currentUser) => {
  debugAuth();
  debugUserContext(currentUser);
  await testApiEndpoint('/proposals');
  await testApiEndpoint('/personnels');
  await testApiEndpoint('/settings');
};

// Add to window for easy access in browser console
if (typeof window !== 'undefined') {
  window.debugAuth = debugAuth;
  window.testApiEndpoint = testApiEndpoint;
  window.debugUserContext = debugUserContext;
  window.fullAuthDebug = fullAuthDebug;
}
