import { QueryClientProvider } from "@tanstack/react-query";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter as Router } from "react-router-dom";
import App from "./App.jsx";
import queryClient from "./config/queryClient.js";
import SearchProvider from "./context/SearchContext.jsx";
import { ThemeProvider } from "./context/ThemeContext.jsx";
import ToastWrapper from "./global/components/ToastWrapper.jsx";
import ErrorBoundary from "./global/components/ErrorBoundary.jsx";
import { serviceWorkerAuth } from "./utils/secureAuth.js";
import "./utils/testServiceWorker.js"; // Import test utilities
import "./index.css";

// 🔒 Initialize Service Worker for secure token storage
const initializeSecureAuth = async () => {
  try {
    // Skip Service Worker initialization if on login page to avoid blocking
    const currentPath = window.location.pathname;
    if (currentPath === '/' || currentPath.includes('/login')) {
      console.log('🔒 Skipping Service Worker init on login page');
      return;
    }

    const success = await serviceWorkerAuth.init();
    if (success) {
      console.log('🔒 Service Worker Auth: READY - Tokens will be COMPLETELY HIDDEN!');
    } else {
      console.warn('🔒 Service Worker failed to initialize, using fallback auth');
    }
  } catch (error) {
    console.error('🔒 Service Worker initialization error:', error);
  }
};

// Initialize Service Worker before rendering app (but not on login page)
initializeSecureAuth();

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <Router>
          {/* Wrap ToastWrapper in an error boundary */}
          <ErrorBoundary>
            <ToastWrapper />
          </ErrorBoundary>
          <SearchProvider>
            <App />
          </SearchProvider>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  </StrictMode>
);
