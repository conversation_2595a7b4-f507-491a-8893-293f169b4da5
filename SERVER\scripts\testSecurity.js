/**
 * Comprehensive Security Testing Script
 * Run this to verify all routes are properly secured
 */

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const SERVER_URL = 'http://localhost:5005';

// Test endpoints with different security levels
const TEST_ENDPOINTS = {
  public: [
    { method: 'GET', path: '/debug/public', expectedStatus: 200 }
  ],
  authenticated: [
    { method: 'GET', path: '/debug/auth', expectedStatus: 401 },
    { method: 'GET', path: '/proposals', expectedStatus: 401 },
    { method: 'GET', path: '/getpersonnels', expectedStatus: 401 },
    { method: 'GET', path: '/settings', expectedStatus: 401 },
    { method: 'GET', path: '/mooe-data', expectedStatus: 401 }
  ],
  admin: [
    { method: 'POST', path: '/proposals', expectedStatus: 401 },
    { method: 'PUT', path: '/proposals/123', expectedStatus: 401 },
    { method: 'DELETE', path: '/proposals/123', expectedStatus: 401 },
    { method: 'POST', path: '/categories', expectedStatus: 401 }
  ]
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

console.log('🔒 Security Testing Script\n');

async function testEndpoint(method, path, expectedStatus, token = null) {
  try {
    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${SERVER_URL}${path}`, {
      method,
      headers
    });

    const success = response.status === expectedStatus;
    const status = success ? 'PASS' : 'FAIL';
    const color = success ? 'green' : 'red';

    log(color, `  ${status}: ${method} ${path} - Expected: ${expectedStatus}, Got: ${response.status}`);

    if (!success) {
      try {
        const text = await response.text();
        console.log(`    Response: ${text.substring(0, 100)}...`);
      } catch (e) {
        console.log(`    Could not read response body`);
      }
    }

    return success;
  } catch (error) {
    log('red', `  ERROR: ${method} ${path} - ${error.message}`);
    return false;
  }
}

async function runSecurityTests() {
  console.log('\n🔒 SECURITY TESTING STARTED');
  console.log('=' .repeat(50));

  let totalTests = 0;
  let passedTests = 0;

  // Test 1: Public endpoints (should work without auth)
  log('blue', '\n📖 Testing Public Endpoints (No Auth Required):');
  for (const test of TEST_ENDPOINTS.public) {
    totalTests++;
    const passed = await testEndpoint(test.method, test.path, test.expectedStatus);
    if (passed) passedTests++;
  }

  // Test 2: Authenticated endpoints without token (should fail)
  log('blue', '\n🔐 Testing Authenticated Endpoints (Should Fail Without Token):');
  for (const test of TEST_ENDPOINTS.authenticated) {
    totalTests++;
    const passed = await testEndpoint(test.method, test.path, test.expectedStatus);
    if (passed) passedTests++;
  }

  // Test 3: Admin endpoints without token (should fail)
  log('blue', '\n👑 Testing Admin Endpoints (Should Fail Without Token):');
  for (const test of TEST_ENDPOINTS.admin) {
    totalTests++;
    const passed = await testEndpoint(test.method, test.path, test.expectedStatus);
    if (passed) passedTests++;
  }

  // Test 4: Check headers endpoint
  log('blue', '\n📋 Testing Headers Endpoint:');
  totalTests++;
  const passed = await testEndpoint('GET', '/debug/headers', 200);
  if (passed) passedTests++;

  return { totalTests, passedTests };
}

// Check if server is running
async function checkServerStatus() {
  try {
    const response = await fetch(`${SERVER_URL}/debug/public`);
    if (response.ok) {
      log('green', '✅ Server is running and accessible');
      return true;
    } else {
      log('red', '❌ Server responded but with error status');
      return false;
    }
  } catch (error) {
    log('red', `❌ Cannot connect to server: ${error.message}`);
    log('yellow', '   Make sure the server is running on http://localhost:5005');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 BUDGET-FMIS SECURITY TEST SUITE');
  console.log('Testing server at:', SERVER_URL);

  // Check if server is running
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    process.exit(1);
  }

  // Run security tests
  const { totalTests, passedTests } = await runSecurityTests();

  // Summary
  console.log('\n' + '=' .repeat(50));
  log('blue', `📊 SECURITY TEST SUMMARY:`);
  log('blue', `   Total Tests: ${totalTests}`);
  log('green', `   Passed: ${passedTests}`);
  log('red', `   Failed: ${totalTests - passedTests}`);

  const percentage = Math.round((passedTests / totalTests) * 100);
  if (percentage >= 90) {
    log('green', `   Success Rate: ${percentage}% ✅ EXCELLENT SECURITY!`);
  } else if (percentage >= 70) {
    log('yellow', `   Success Rate: ${percentage}% ⚠️  GOOD BUT NEEDS IMPROVEMENT`);
  } else {
    log('red', `   Success Rate: ${percentage}% ❌ SECURITY ISSUES DETECTED!`);
  }

  console.log('\n🔍 MANUAL TESTS TO PERFORM:');
  console.log('1. Test with valid user token (should work for GET endpoints)');
  console.log('2. Test with admin token (should work for all endpoints)');
  console.log('3. Test with expired token (should fail)');
  console.log('4. Test role-based access (user token on admin endpoints)');

  console.log('\n📖 For manual testing:');
  console.log('   - Open browser and visit: http://localhost:5005/debug/public');
  console.log('   - Try: http://localhost:5005/debug/auth (should fail)');
  console.log('   - Use browser console to test with tokens');

  return percentage >= 90;
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}
