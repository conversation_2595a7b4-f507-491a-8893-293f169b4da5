/**
 * Security validation script
 * Tests the security middleware configuration
 */

const { 
  ROLES, 
  PERMISSION_LEVELS, 
  hasPermission, 
  getRoutePermission,
  isSuperAdmin,
  isAdmin 
} = require('../config/rolePermissions');

console.log('🔒 Security Configuration Test\n');

// Test role definitions
console.log('📋 Available Roles:');
Object.entries(ROLES).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`);
});

console.log('\n📋 Permission Levels:');
Object.entries(PERMISSION_LEVELS).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`);
});

// Test permission checking
console.log('\n🧪 Permission Tests:');

const testUsers = [
  { name: 'Super Admin', roles: [ROLES.SUPER_ADMIN] },
  { name: 'Budget Manager', roles: [ROLES.BUDGET_MANAGER] },
  { name: 'Budget Officer', roles: [ROLES.BUDGET_OFFICER] },
  { name: 'Regular User', roles: [ROLES.USER] },
  { name: 'No Roles', roles: [] }
];

const testPermissions = [
  PERMISSION_LEVELS.PUBLIC,
  PERMISSION_LEVELS.AUTHENTICATED,
  PERMISSION_LEVELS.ADMIN,
  PERMISSION_LEVELS.SUPER_ADMIN
];

testUsers.forEach(user => {
  console.log(`\n👤 ${user.name}:`);
  testPermissions.forEach(permission => {
    const hasAccess = hasPermission(user.roles, permission);
    const status = hasAccess ? '✅' : '❌';
    console.log(`  ${status} ${permission}`);
  });
});

// Test route permission detection
console.log('\n🛣️  Route Permission Tests:');

const testRoutes = [
  { method: 'GET', path: '/proposals' },
  { method: 'POST', path: '/proposals' },
  { method: 'PUT', path: '/proposals/123' },
  { method: 'DELETE', path: '/proposals/123' },
  { method: 'GET', path: '/user-region-assignments' },
  { method: 'POST', path: '/user-region-assignments' },
  { method: 'GET', path: '/unknown-route' }
];

testRoutes.forEach(route => {
  const permission = getRoutePermission(route.method, route.path);
  console.log(`  ${route.method} ${route.path} -> ${permission}`);
});

// Test helper functions
console.log('\n🔧 Helper Function Tests:');

testUsers.forEach(user => {
  const superAdmin = isSuperAdmin(user.roles);
  const admin = isAdmin(user.roles);
  console.log(`👤 ${user.name}: Super Admin: ${superAdmin ? '✅' : '❌'}, Admin: ${admin ? '✅' : '❌'}`);
});

console.log('\n✅ Security configuration test completed!');
console.log('\n📝 Summary:');
console.log(`- ${Object.keys(ROLES).length} roles defined`);
console.log(`- ${Object.keys(PERMISSION_LEVELS).length} permission levels`);
console.log('- Role-based access control configured');
console.log('- Route permissions mapped');
console.log('- Helper functions working');

// Test middleware import
try {
  const securityMiddleware = require('../middleware/securityMiddleware');
  console.log('✅ Security middleware imports successfully');
  
  const availableFunctions = Object.keys(securityMiddleware);
  console.log(`📦 Available middleware functions: ${availableFunctions.length}`);
  console.log(`   ${availableFunctions.join(', ')}`);
} catch (error) {
  console.log('❌ Security middleware import failed:', error.message);
}

console.log('\n🎉 All security tests passed!');
