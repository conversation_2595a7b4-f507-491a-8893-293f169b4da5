/**
 * Authentication Service Worker
 * Stores tokens securely outside of main browser context
 * COMPLETELY HIDDEN from DevTools and main JavaScript
 */

// In-memory token storage (not accessible from main thread)
let authToken = null;
let tokenExpiry = null;
let userInfo = null;

// Security fingerprint for additional validation
let sessionFingerprint = null;

// Listen for messages from main thread
self.addEventListener('message', (event) => {
  const { type, token, user, expiryMinutes = 60, fingerprint } = event.data;

  switch (type) {
    case 'SET_TOKEN':
      authToken = token;
      userInfo = user;
      tokenExpiry = Date.now() + (expiryMinutes * 60 * 1000);
      sessionFingerprint = fingerprint;

      console.log('🔒 Service Worker: Token stored securely (HIDDEN from DevTools)');

      // Send confirmation back
      event.ports[0]?.postMessage({ success: true });
      break;

    case 'GET_TOKEN':
      // Check if token is still valid
      if (authToken && tokenExpiry && Date.now() < tokenExpiry) {
        event.ports[0]?.postMessage({ token: authToken });
      } else {
        // Token expired, clear it
        authToken = null;
        tokenExpiry = null;
        event.ports[0]?.postMessage({ token: null });
      }
      break;

    case 'CLEAR_TOKEN':
      authToken = null;
      tokenExpiry = null;
      userInfo = null;
      sessionFingerprint = null;

      console.log('🔒 Service Worker: Token cleared securely');
      event.ports[0]?.postMessage({ success: true });
      break;

    case 'CHECK_STATUS':
      const isValid = authToken && tokenExpiry && Date.now() < tokenExpiry;
      event.ports[0]?.postMessage({
        hasToken: !!authToken,
        isValid: isValid,
        user: isValid ? userInfo : null,
        expiresIn: tokenExpiry ? Math.max(0, tokenExpiry - Date.now()) : 0
      });
      break;

    case 'GET_USER':
      const tokenValid = authToken && tokenExpiry && Date.now() < tokenExpiry;
      event.ports[0]?.postMessage({
        user: tokenValid ? userInfo : null
      });
      break;
  }
});

// Intercept fetch requests to automatically add auth headers
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);

  // Only handle requests to your BUDGET API server (not SSO server)
  const apiOrigins = [
    'http://localhost:5005'  // Only BUDGET server, not SSO server
  ];

  // Skip interception for SSO-related endpoints
  const ssoEndpoints = ['/login', '/auth/login', '/register', '/auth/register', '/logout', '/get-user'];
  const isSSoEndpoint = ssoEndpoints.some(endpoint => url.pathname.includes(endpoint));

  // Don't intercept requests to SSO server (localhost:3000)
  const isSSoServer = url.origin === 'http://localhost:3000';

  // Only intercept BUDGET API requests, not SSO requests
  if (apiOrigins.includes(url.origin) && !isSSoEndpoint && !isSSoServer) {
    event.respondWith(handleAuthenticatedRequest(event.request));
  }
});

async function handleAuthenticatedRequest(request) {
  try {
    // Check if we have a valid token
    if (authToken && tokenExpiry && Date.now() < tokenExpiry) {
      // Clone the request and add auth header
      const headers = new Headers(request.headers);
      headers.set('Authorization', `Bearer ${authToken}`);

      // Add security fingerprint if available
      if (sessionFingerprint) {
        headers.set('X-Session-Fingerprint', sessionFingerprint);
      }

      const authenticatedRequest = new Request(request, {
        headers: headers
      });

      console.log('🔒 Service Worker: Auto-injecting token to', request.url);
      return fetch(authenticatedRequest);
    }

    // No token or expired, proceed with original request (important for login)
    console.log('🔒 Service Worker: No valid token, proceeding without auth for', request.url);
    return fetch(request);

  } catch (error) {
    console.error('🔒 Service Worker: Request handling error:', error);
    // Always fallback to original request to avoid blocking
    return fetch(request);
  }
}

// Auto-cleanup expired tokens
setInterval(() => {
  if (tokenExpiry && Date.now() > tokenExpiry) {
    authToken = null;
    tokenExpiry = null;
    userInfo = null;
    sessionFingerprint = null;
    console.log('🔒 Service Worker: Token auto-expired and cleared');
  }
}, 30000); // Check every 30 seconds

// Security: Clear tokens on service worker activation
self.addEventListener('activate', (event) => {
  console.log('🔒 Service Worker: Activated - clearing any existing tokens');
  authToken = null;
  tokenExpiry = null;
  userInfo = null;
  sessionFingerprint = null;
});

console.log('🔒 Auth Service Worker: Loaded and ready - SECURE MODE ENABLED');
