/**
 * Authentication Service Worker
 * Stores tokens securely outside of main browser context
 */

// In-memory token storage (not accessible from main thread)
let authToken = null;
let tokenExpiry = null;

// Listen for messages from main thread
self.addEventListener('message', (event) => {
  const { type, token, expiryMinutes = 60 } = event.data;

  switch (type) {
    case 'SET_TOKEN':
      authToken = token;
      tokenExpiry = Date.now() + (expiryMinutes * 60 * 1000);
      
      // Send confirmation back
      event.ports[0]?.postMessage({ success: true });
      break;

    case 'GET_TOKEN':
      // Check if token is still valid
      if (authToken && tokenExpiry && Date.now() < tokenExpiry) {
        event.ports[0]?.postMessage({ token: authToken });
      } else {
        // Token expired, clear it
        authToken = null;
        tokenExpiry = null;
        event.ports[0]?.postMessage({ token: null });
      }
      break;

    case 'CLEAR_TOKEN':
      authToken = null;
      tokenExpiry = null;
      event.ports[0]?.postMessage({ success: true });
      break;

    case 'CHECK_STATUS':
      const isValid = authToken && tokenExpiry && Date.now() < tokenExpiry;
      event.ports[0]?.postMessage({ 
        hasToken: !!authToken,
        isValid: isValid,
        expiresIn: tokenExpiry ? Math.max(0, tokenExpiry - Date.now()) : 0
      });
      break;
  }
});

// Intercept fetch requests to automatically add auth headers
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Only handle requests to your API
  if (url.origin === 'http://localhost:5005' || url.origin === 'http://localhost:3001') {
    event.respondWith(handleAuthenticatedRequest(event.request));
  }
});

async function handleAuthenticatedRequest(request) {
  // Check if we have a valid token
  if (authToken && tokenExpiry && Date.now() < tokenExpiry) {
    // Clone the request and add auth header
    const headers = new Headers(request.headers);
    headers.set('Authorization', `Bearer ${authToken}`);
    
    const authenticatedRequest = new Request(request, {
      headers: headers
    });
    
    return fetch(authenticatedRequest);
  }
  
  // No token or expired, proceed with original request
  return fetch(request);
}

// Auto-cleanup expired tokens
setInterval(() => {
  if (tokenExpiry && Date.now() > tokenExpiry) {
    authToken = null;
    tokenExpiry = null;
    console.log('Service Worker: Token auto-expired');
  }
}, 60000); // Check every minute

console.log('Auth Service Worker: Loaded and ready');
