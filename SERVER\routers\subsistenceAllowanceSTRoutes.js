const {
  createSubsistenceST,
  getAllSubsistenceST,
  updateSubsistenceST,
  deleteSubsistenceST,
} = require("../controllers/subsistenceAllowanceSTController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const subsistenceSTRouter = Router();

subsistenceSTRouter.get("/subsistence-allowance-st", getAllSubsistenceST);
subsistenceSTRouter.post("/subsistence-allowance-st", checkDueDate, createSubsistenceST);
subsistenceSTRouter.put("/subsistence-allowance-st/:id", checkDueDate, updateSubsistenceST);
subsistenceSTRouter.delete("/subsistence-allowance-st/:id", checkDueDate, deleteSubsistenceST);

module.exports = subsistenceSTRouter;
