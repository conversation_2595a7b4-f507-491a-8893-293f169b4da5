const {
  createSubsistenceAllowance,
  getAllSubsistenceAllowances,
  updateSubsistenceAllowance,
  deleteSubsistenceAllowance,
  getAllPerServicesMDS,
} = require("../controllers/subsistenceAllowanceMDSController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const subsistenceAllowanceRouter = Router();

subsistenceAllowanceRouter.get("/subsistence-allowance-mds", getAllSubsistenceAllowances);
subsistenceAllowanceRouter.post("/subsistence-allowance-mds", checkDueDate, createSubsistenceAllowance);
subsistenceAllowanceRouter.put("/subsistence-allowance-mds/:id", checkDueDate, updateSubsistenceAllowance);
subsistenceAllowanceRouter.delete("/subsistence-allowance-mds/:id", checkDueDate, deleteSubsistenceAllowance);
subsistenceAllowanceRouter.get("/getpersonnelsmds", getAllPerServicesMDS);

module.exports = subsistenceAllowanceRouter;
