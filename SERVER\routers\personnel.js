const express = require('express');
const router = express.Router();
const { getAllPersonnel, addPersonnel } = require('../controllers/personnelController');

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get all personnel - Authenticated users only
router.get('/personnels', ...authenticatedRoute(), getAllPersonnel);

// Add personnel - Admin only
router.post('/addpersonnel', ...adminRoute(), addPersonnel);

module.exports = router;