/**
 * 🔒 Security Configuration
 * Choose your preferred security level
 */

export const SECURITY_MODES = {
  // Maximum security - Service Worker only (single browser)
  MAXIMUM: 'MAXIMUM',
  
  // Balanced security - Service Worker + encrypted localStorage fallback
  BALANCED: 'BALANCED',
  
  // Cross-browser - encrypted localStorage only
  CROSS_BROWSER: 'CROSS_BROWSER',
  
  // Basic - plain localStorage (not recommended)
  BASIC: 'BASIC'
};

// 🔧 CHANGE THIS TO YOUR PREFERRED MODE
export const CURRENT_SECURITY_MODE = SECURITY_MODES.BALANCED;

export const securityConfig = {
  // Current mode settings
  mode: CURRENT_SECURITY_MODE,
  
  // Mode descriptions
  descriptions: {
    [SECURITY_MODES.MAXIMUM]: {
      name: 'Maximum Security',
      description: 'Service Worker only - tokens completely hidden from DevTools',
      crossBrowser: false,
      devToolsVisible: false,
      jsAccessible: false,
      xssProtection: 'Excellent',
      usability: 'Single browser only'
    },
    
    [SECURITY_MODES.BALANCED]: {
      name: 'Balanced Security',
      description: 'Service Worker + encrypted localStorage fallback',
      crossBrowser: true,
      devToolsVisible: 'Encrypted only',
      jsAccessible: false,
      xssProtection: 'Very Good',
      usability: 'Cross-browser with enhanced security'
    },
    
    [SECURITY_MODES.CROSS_BROWSER]: {
      name: 'Cross-Browser',
      description: 'Encrypted localStorage only',
      crossBrowser: true,
      devToolsVisible: 'Encrypted',
      jsAccessible: false,
      xssProtection: 'Good',
      usability: 'Works everywhere'
    },
    
    [SECURITY_MODES.BASIC]: {
      name: 'Basic Security',
      description: 'Plain localStorage (not recommended)',
      crossBrowser: true,
      devToolsVisible: true,
      jsAccessible: true,
      xssProtection: 'Poor',
      usability: 'Maximum compatibility'
    }
  },
  
  // Get current mode info
  getCurrentMode() {
    return this.descriptions[this.mode];
  },
  
  // Check if Service Worker should be used
  shouldUseServiceWorker() {
    return this.mode === SECURITY_MODES.MAXIMUM || this.mode === SECURITY_MODES.BALANCED;
  },
  
  // Check if localStorage fallback should be used
  shouldUseLocalStorageFallback() {
    return this.mode === SECURITY_MODES.BALANCED || this.mode === SECURITY_MODES.CROSS_BROWSER;
  },
  
  // Check if encryption should be used
  shouldEncrypt() {
    return this.mode !== SECURITY_MODES.BASIC;
  },
  
  // Get security report
  getSecurityReport() {
    const currentMode = this.getCurrentMode();
    const serviceWorkerSupported = 'serviceWorker' in navigator;
    const serviceWorkerReady = window.serviceWorkerAuth?.isReady || false;
    
    return {
      configuredMode: this.mode,
      currentMode: currentMode,
      serviceWorkerSupported,
      serviceWorkerReady,
      effectiveMode: this.shouldUseServiceWorker() && serviceWorkerReady 
        ? 'Service Worker' 
        : 'localStorage',
      recommendations: this.getRecommendations()
    };
  },
  
  // Get security recommendations
  getRecommendations() {
    const recommendations = [];
    
    if (this.mode === SECURITY_MODES.BASIC) {
      recommendations.push('⚠️ Consider upgrading to BALANCED mode for better security');
    }
    
    if (!('serviceWorker' in navigator) && this.shouldUseServiceWorker()) {
      recommendations.push('⚠️ Service Worker not supported, using fallback storage');
    }
    
    if (this.mode === SECURITY_MODES.MAXIMUM) {
      recommendations.push('ℹ️ Maximum security mode - tokens only work in current browser');
    }
    
    if (this.mode === SECURITY_MODES.BALANCED) {
      recommendations.push('✅ Balanced mode provides good security with cross-browser support');
    }
    
    return recommendations;
  }
};

// Export current mode for easy access
export default securityConfig;
