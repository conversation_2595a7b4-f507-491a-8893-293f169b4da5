const {
  getAllRegions,
  getRegionById,
  createRegion,
  updateRegion,
  deleteRegion,
} = require("../controllers/RegionController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const regionRouter = Router();

// 🔒 SECURED ROUTES

// Get all regions - Authenticated users only
regionRouter.get("/regions", ...authenticatedRoute(), getAllRegions);

// Get a region by ID - Authenticated users only
regionRouter.get("/regions/:id", ...authenticatedRoute(), getRegionById);

// Add a new region - Admin only
regionRouter.post("/postregions", ...adminRoute(), createRegion);

// Edit an existing region - Admin only
regionRouter.put("/regions/:id", ...adminRoute(), updateRegion);

// Delete a region - Admin only
regionRouter.delete("/regions/:id", ...adminRoute(), deleteRegion);

module.exports = regionRouter;
