import React, { useState, useEffect } from 'react';
import { useUser } from '../context/UserContext';
import { debugAuth, testApiEndpoint, debugUserContext } from '../utils/debugAuth';
import { serviceWorkerAuth } from '../utils/secureAuth';
import securityConfig from '../config/securityConfig';
import SecurityModeSelector from './SecurityModeSelector';
import api from '../config/api';

const DebugAuth = () => {
  const { currentUser } = useUser();
  const [testResults, setTestResults] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState({});
  const [securityReport, setSecurityReport] = useState({});

  const runDebug = async () => {
    console.clear();
    debugAuth();
    debugUserContext(currentUser);

    // Check Service Worker status
    await checkServiceWorkerStatus();
    await generateSecurityReport();
  };

  const checkServiceWorkerStatus = async () => {
    try {
      const status = await serviceWorkerAuth.checkStatus();
      const isControlling = serviceWorkerAuth.isControlling();

      setServiceWorkerStatus({
        isReady: serviceWorkerAuth.isReady,
        isControlling: isControlling,
        hasToken: status.hasToken,
        isValid: status.isValid,
        expiresIn: status.expiresIn,
        user: status.user
      });

      console.log('🔒 Service Worker Status:', {
        isReady: serviceWorkerAuth.isReady,
        isControlling: isControlling,
        hasToken: status.hasToken,
        isValid: status.isValid,
        expiresIn: Math.round(status.expiresIn / 1000) + 's'
      });
    } catch (error) {
      console.error('🔒 Service Worker status check failed:', error);
    }
  };

  const generateSecurityReport = async () => {
    const report = {
      timestamp: new Date().toISOString(),
      localStorage: !!localStorage.getItem('token'),
      sessionStorage: !!sessionStorage.getItem('token'),
      serviceWorker: serviceWorkerAuth.isReady,
      serviceWorkerControlling: serviceWorkerAuth.isControlling(),
      tokenVisibility: {
        devTools: !!localStorage.getItem('token') || !!sessionStorage.getItem('token'),
        console: !!localStorage.getItem('token'),
        serviceWorker: serviceWorkerStatus.hasToken
      },
      securityLevel: serviceWorkerAuth.isReady ? 'MAXIMUM' : 'BASIC',
      recommendation: serviceWorkerAuth.isReady
        ? '✅ Token COMPLETELY HIDDEN from DevTools'
        : '⚠️ Token may be visible in DevTools'
    };

    setSecurityReport(report);
    console.log('🔒 Security Report:', report);
  };

  const testEndpoint = async (endpoint) => {
    setIsLoading(true);
    try {
      const response = await api.get(endpoint);
      setTestResults(prev => ({
        ...prev,
        [endpoint]: { success: true, status: response.status, data: response.data }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [endpoint]: { 
          success: false, 
          status: error.response?.status, 
          message: error.response?.data?.message || error.message 
        }
      }));
    }
    setIsLoading(false);
  };

  const testEndpoints = [
    '/proposals',
    '/personnels', 
    '/settings',
    '/mooe-data',
    '/categories'
  ];

  useEffect(() => {
    // Auto-run debug on component mount
    runDebug();
  }, [currentUser]);

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '2px solid #ccc', 
      padding: '15px', 
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
      zIndex: 9999,
      maxWidth: '400px',
      fontSize: '12px'
    }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>🔍 Auth Debug Panel</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>User Status:</strong> {currentUser ? '✅ Logged In' : '❌ Not Logged In'}
      </div>
      
      {currentUser && (
        <div style={{ marginBottom: '10px' }}>
          <strong>Roles:</strong> {JSON.stringify(currentUser.Roles || currentUser.roles)}
        </div>
      )}
      
      <div style={{ marginBottom: '10px' }}>
        <strong>localStorage Token:</strong> {localStorage.getItem('token') ? '⚠️ VISIBLE' : '✅ Hidden'}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Service Worker:</strong> {serviceWorkerStatus.isReady ? '✅ Ready' : '❌ Not Ready'}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>SW Token:</strong> {serviceWorkerStatus.hasToken ? '✅ Present (HIDDEN)' : '❌ Missing'}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <strong>Security Level:</strong> {securityReport.securityLevel || 'Unknown'}
      </div>
      
      <button
        onClick={runDebug}
        style={{
          padding: '5px 10px',
          marginRight: '5px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Run Debug
      </button>

      <button
        onClick={checkServiceWorkerStatus}
        style={{
          padding: '5px 10px',
          marginRight: '5px',
          backgroundColor: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Check SW
      </button>

      <button
        onClick={async () => {
          try {
            const existingToken = localStorage.getItem("token");
            if (existingToken && currentUser) {
              const success = await serviceWorkerAuth.setToken(existingToken, currentUser, 60);
              if (success) {
                localStorage.removeItem("token");
                console.log('🔒 Token migrated to Service Worker and localStorage cleared');
                await checkServiceWorkerStatus();
              }
            }
          } catch (error) {
            console.error('Migration failed:', error);
          }
        }}
        style={{
          padding: '5px 10px',
          marginRight: '5px',
          backgroundColor: '#ffc107',
          color: 'black',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Migrate Token
      </button>
      
      <div style={{ marginTop: '10px' }}>
        <strong>Test Endpoints:</strong>
        {testEndpoints.map(endpoint => (
          <div key={endpoint} style={{ margin: '5px 0' }}>
            <button 
              onClick={() => testEndpoint(endpoint)}
              disabled={isLoading}
              style={{ 
                padding: '2px 8px', 
                marginRight: '5px',
                backgroundColor: '#28a745', 
                color: 'white', 
                border: 'none', 
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Test
            </button>
            <span style={{ fontSize: '10px' }}>{endpoint}</span>
            {testResults[endpoint] && (
              <span style={{ 
                marginLeft: '5px', 
                color: testResults[endpoint].success ? 'green' : 'red',
                fontSize: '10px'
              }}>
                {testResults[endpoint].success ? '✅' : '❌'} 
                {testResults[endpoint].status}
                {!testResults[endpoint].success && ` - ${testResults[endpoint].message}`}
              </span>
            )}
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '10px', color: '#666' }}>
        Check browser console for detailed logs
      </div>

      {/* Security Mode Selector */}
      <div style={{ marginTop: '15px', borderTop: '1px solid #ccc', paddingTop: '15px' }}>
        <SecurityModeSelector />
      </div>
    </div>
  );
};

export default DebugAuth;
