import React, { useState, useEffect } from 'react';
import { useUser } from '../context/UserContext';
import { debugAuth, testApiEndpoint, debugUserContext } from '../utils/debugAuth';
import api from '../config/api';

const DebugAuth = () => {
  const { currentUser } = useUser();
  const [testResults, setTestResults] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const runDebug = () => {
    console.clear();
    debugAuth();
    debugUserContext(currentUser);
  };

  const testEndpoint = async (endpoint) => {
    setIsLoading(true);
    try {
      const response = await api.get(endpoint);
      setTestResults(prev => ({
        ...prev,
        [endpoint]: { success: true, status: response.status, data: response.data }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [endpoint]: { 
          success: false, 
          status: error.response?.status, 
          message: error.response?.data?.message || error.message 
        }
      }));
    }
    setIsLoading(false);
  };

  const testEndpoints = [
    '/proposals',
    '/personnels', 
    '/settings',
    '/mooe-data',
    '/categories'
  ];

  useEffect(() => {
    // Auto-run debug on component mount
    runDebug();
  }, [currentUser]);

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '2px solid #ccc', 
      padding: '15px', 
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
      zIndex: 9999,
      maxWidth: '400px',
      fontSize: '12px'
    }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>🔍 Auth Debug Panel</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>User Status:</strong> {currentUser ? '✅ Logged In' : '❌ Not Logged In'}
      </div>
      
      {currentUser && (
        <div style={{ marginBottom: '10px' }}>
          <strong>Roles:</strong> {JSON.stringify(currentUser.Roles || currentUser.roles)}
        </div>
      )}
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Token:</strong> {localStorage.getItem('token') ? '✅ Present' : '❌ Missing'}
      </div>
      
      <button 
        onClick={runDebug}
        style={{ 
          padding: '5px 10px', 
          marginRight: '5px', 
          backgroundColor: '#007bff', 
          color: 'white', 
          border: 'none', 
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Run Debug
      </button>
      
      <div style={{ marginTop: '10px' }}>
        <strong>Test Endpoints:</strong>
        {testEndpoints.map(endpoint => (
          <div key={endpoint} style={{ margin: '5px 0' }}>
            <button 
              onClick={() => testEndpoint(endpoint)}
              disabled={isLoading}
              style={{ 
                padding: '2px 8px', 
                marginRight: '5px',
                backgroundColor: '#28a745', 
                color: 'white', 
                border: 'none', 
                borderRadius: '3px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Test
            </button>
            <span style={{ fontSize: '10px' }}>{endpoint}</span>
            {testResults[endpoint] && (
              <span style={{ 
                marginLeft: '5px', 
                color: testResults[endpoint].success ? 'green' : 'red',
                fontSize: '10px'
              }}>
                {testResults[endpoint].success ? '✅' : '❌'} 
                {testResults[endpoint].status}
                {!testResults[endpoint].success && ` - ${testResults[endpoint].message}`}
              </span>
            )}
          </div>
        ))}
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '10px', color: '#666' }}>
        Check browser console for detailed logs
      </div>
    </div>
  );
};

export default DebugAuth;
