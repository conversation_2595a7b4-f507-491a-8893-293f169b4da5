const express = require("express");
const router = express.Router();
const incomeController = require("../controllers/IncomeController");

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get all income entries - Authenticated users only
router.get("/income", ...authenticatedRoute(), incomeController.getAllIncome);

// Get income by ID - Authenticated users only
router.get("/income/:id", ...authenticatedRoute(), incomeController.getIncomeById);

// Add a test endpoint - Authenticated users only
router.get("/income/test", ...authenticatedRoute(), (req, res) => {
  console.log("Income test endpoint hit");
  res.status(200).json({ message: "Income API is working" });
});

// Create a new income entry - Admin only
router.post("/income", ...adminRoute(), incomeController.createIncome);

// Update an income entry - Admin only with due date protection
router.put("/income/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), incomeController.updateIncome);

// Delete income - Admin only with due date protection
router.delete("/income/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), incomeController.deleteIncome);

module.exports = router;
