const {
  getAllCapitalOutlays,
  addCapitalOutlay,
  editCapitalOutlay,
  deleteCapitalOutlay,
  getCapitalOutlayList,
  getSublineItems,
  getAccountingTitles,
  deleteAllCapitalOutlays,
} = require("../controllers/capitalOutlayController");

const Router = require("express").Router;
const CapitalOutlay = require("../models/CapitalOutlay"); // New model import

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const capitalOutlayRouter = Router();

// 🔒 SECURED ROUTES

// Get routes - Authenticated users only
capitalOutlayRouter.get("/capital-outlays", ...authenticatedRoute(), getAllCapitalOutlays);
capitalOutlayRouter.get("/capital-outlay-list", ...authenticatedRoute(), getCapitalOutlayList);
capitalOutlayRouter.get("/subline-items", ...authenticatedRoute(), getSublineItems);
capitalOutlayRouter.get("/accounting-titles", ...authenticatedRoute(), getAccountingTitles);

// Admin routes with due date protection
capitalOutlayRouter.post("/capital-outlays", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), addCapitalOutlay);
capitalOutlayRouter.put("/capital-outlays/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), editCapitalOutlay);
capitalOutlayRouter.delete("/capital-outlays/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteCapitalOutlay);

// Admin only routes
capitalOutlayRouter.delete("/deleteAllCapitalOutlays", ...adminRoute(), deleteAllCapitalOutlays);
// Get Capital Outlay by parameters - Authenticated users only
capitalOutlayRouter.get("/capital-outlay/getByParams", ...authenticatedRoute(), async (req, res) => {
  try {
    const { fiscalYear, budgetType, processBy, region, status } = req.query;

    console.log("Fetching Capital Outlay with params:", { fiscalYear, budgetType, processBy, region, status });

    const query = {};
    if (fiscalYear) query.fiscalYear = fiscalYear;
    if (budgetType) query.budgetType = budgetType;
    if (processBy) query.processBy = processBy;
    if (region) query.region = region;
    if (status) query.status = status;

    const capitalOutlay = await CapitalOutlay.find(query).lean();

    console.log(`Found ${capitalOutlay.length} Capital Outlay records`);
    res.status(200).json(capitalOutlay);
  } catch (error) {
    console.error("Error fetching Capital Outlay by params:", error);
    res.status(500).json({ message: "Failed to fetch Capital Outlay data" });
  }
});
module.exports = capitalOutlayRouter;
