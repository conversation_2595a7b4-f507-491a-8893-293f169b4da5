const {
    createMealAllowance,
    getAllMealAllowances,
    updateMealAllowance,
    deleteMealAllowance,
  } = require("../controllers/mealAllowanceController");
  const Router = require("express").Router;
  const checkDueDate = require("../middleware/checkDueDate");
  
  const mealAllowanceRouter = Router();
  
  // List all meal allowances
  mealAllowanceRouter.get("/meal-allowance", getAllMealAllowances);
  // Create a meal allowance entry
  mealAllowanceRouter.post(
    "/meal-allowance",
    checkDueDate,
    createMealAllowance
  );
  // Update a meal allowance entry
  mealAllowanceRouter.put(
    "/meal-allowance/:id",
    checkDueDate,
    updateMealAllowance
  );
  // Delete a meal allowance entry
  mealAllowanceRouter.delete(
    "/meal-allowance/:id",
    checkDueDate,
    deleteMealAllowance
  );
  
  module.exports = mealAllowanceRouter;
  