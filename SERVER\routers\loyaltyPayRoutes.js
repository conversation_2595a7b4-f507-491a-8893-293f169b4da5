const {
  createLoyaltyPay,
  getAllLoyaltyPays,
  updateLoyaltyPay,
  deleteLoyaltyPay,
} = require("../controllers/loyaltyPayController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const loyaltyPayRouter = Router();

// 🔒 SECURED ROUTES

// Get all loyalty pays - Authenticated users only
loyaltyPayRouter.get("/loyalty-pay", ...authenticatedRoute(), getAllLoyaltyPays);

// Admin routes with due date protection
loyaltyPayRouter.post("/loyalty-pay", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), createLoyaltyPay);
loyaltyPayRouter.put("/loyalty-pay/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateLoyaltyPay);
loyaltyPayRouter.delete("/loyalty-pay/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteLoyaltyPay);

module.exports = loyaltyPayRouter;
