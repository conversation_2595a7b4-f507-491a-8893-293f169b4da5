const {
  getAllSpecialCounselAllowances,
  getSpecialCounselAllowanceById,
  createSpecialCounselAllowance,
  updateSpecialCounselAllowance,
  deleteSpecialCounselAllowance,
} = require("../controllers/SpecialCounselAllowanceController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");

const specialCounselAllowanceRouter = Router();

specialCounselAllowanceRouter.get("/specialCounselAllowances", getAllSpecialCounselAllowances);
specialCounselAllowanceRouter.get("/specialCounselAllowances/:id", getSpecialCounselAllowanceById);
specialCounselAllowanceRouter.post("/specialCounselAllowances", checkDueDate, createSpecialCounselAllowance);
specialCounselAllowanceRouter.put("/specialCounselAllowances/:id", checkDueDate, updateSpecialCounselAllowance);
specialCounselAllowanceRouter.delete("/specialCounselAllowances/:id", checkDueDate, deleteSpecialCounselAllowance);

module.exports = specialCounselAllowanceRouter;
