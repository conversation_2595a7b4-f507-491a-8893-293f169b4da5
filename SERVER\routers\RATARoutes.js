const {
  getAllRATAs,
  getRATAById,
  createRA<PERSON>,
  updateRA<PERSON>,
  deleteRATA,
} = require("../controllers/RATAController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const rataRouter = Router();

// 🔒 SECURED ROUTES

// Get all RATAs - Authenticated users only
rataRouter.get("/ratas", ...authenticatedRoute(), getAllRATAs);

// Get a RATA by ID - Authenticated users only
rataRouter.get("/ratas/:id", ...authenticatedRoute(), getRATAById);

// Add a new RATA - Admin only
rataRouter.post("/ratas", ...adminRoute(), createRATA);

// Edit an existing RATA - Admin only
rataRouter.put("/ratas/:id", ...adminRoute(), updateRA<PERSON>);

// Delete a RATA - Admin only
rataRouter.delete("/ratas/:id", ...adminRoute(), deleteRATA);

module.exports = rataRouter;