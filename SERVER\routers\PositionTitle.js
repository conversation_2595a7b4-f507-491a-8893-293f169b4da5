const express = require('express');
const positionrouter = express.Router();
const { getAllPositionTitles, getPositionTitleById  } = require('../controllers/PositionTitleController.js');

// Import security middleware
const {
  authenticatedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

// 🔒 SECURED ROUTES

// Get all position titles - Authenticated users only
positionrouter.get('/positions', ...authenticatedRoute(), getAllPositionTitles);

// Get position title by ID - Authenticated users only
positionrouter.get('/byidpositions', ...authenticatedRoute(), getPositionTitleById);

module.exports = positionrouter;


